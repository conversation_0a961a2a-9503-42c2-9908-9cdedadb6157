<script setup>
import { ref, watch, nextTick } from 'vue'
import { ElMessageBox, ElTooltip } from 'element-plus'
import { post, get } from '@/request.js'
import { useUserStore } from '@/store/user'

import shareIcon from '@/assets/image/ai-video/share.png'
import deleteIcon from '@/assets/image/ai-video/delete.png'
import downloadIcon from '@/assets/image/ai-video/download.png'

const userStore = useUserStore()

// Props
const props = defineProps({
  messages: Array,
  isLoading: Boolean,
  hasMoreItems: Boolean,
  currentPage: Number,
  apiEndpoints: Object,
  activeTab: String
})

// Emits
const emit = defineEmits(['refresh-history'])

// 状态
const userInput = ref('')
const isGenerating = ref(false)
const messageContainerRef = ref(null)

// 操作按钮
const operationButtons = ref([
  { id: 'share', icon: shareIcon, tooltip: '分享' },
  { id: 'delete', icon: deleteIcon, tooltip: '删除' },
  { id: 'download', icon: downloadIcon, tooltip: '下载' }
])

// 方法
const formatDateTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

const handleMainScroll = () => {
  if (!messageContainerRef.value) return
  const el = messageContainerRef.value
  if (el.scrollHeight - el.scrollTop - el.clientHeight < 200) {
    if (props.hasMoreItems && !props.isLoading) {
      emit('refresh-history', true)
    }
  }
}

const escapeHtml = (unsafe) => {
  if (typeof unsafe !== 'string') return ''
  return unsafe
    .replace(/&/g, "&")
    .replace(/</g, "<")
    .replace(/>/g, ">")
    .replace(/"/g, "\"")
    .replace(/'/g, "&#039;")
}

const formatPrompt = (messagePrompt) => {
  if (!messagePrompt) return ''
  const mainHtml = escapeHtml(messagePrompt)
  return `<span>${mainHtml}</span>`
}

const handleSendMessage = async () => {
  if (!userInput.value.trim() || isGenerating.value) return

  isGenerating.value = true

  const userPrompt = userInput.value
  let payload = {
    prompt: userPrompt,
    aspect_ratio: '16:9' // 默认比例
  }

  const newMessage = {
    type: 'ai',
    prompt: userPrompt,
    timestamp: formatDateTime(),
    status: 'initiating',
    taskId: null,
    imageUrl: '',
    videoUrl: '',
    isFromHistory: false,
    messageId: `message-${Date.now()}`
  }

  const messageIndex = props.messages.length
  props.messages.push(newMessage)

  await nextTick()
  if (messageContainerRef.value) {
    messageContainerRef.value.scrollTop = 0
  }

  try {
    const data = await post(props.apiEndpoints[props.activeTab].generate, payload, { showError: false })

    if (data.code !== 200) {
      if (props.messages[messageIndex]) {
        props.messages.splice(messageIndex, 1)
      }
      ElMessageBox.alert(data.message || '任务创建失败', '错误', {
        confirmButtonText: '确定',
        type: 'error',
      })
      isGenerating.value = false
      return
    }

    const taskId = data.task_id
    if (props.messages[messageIndex]) {
      props.messages[messageIndex].taskId = taskId
      props.messages[messageIndex].status = 'processing'
      props.messages[messageIndex].messageId = `message-${taskId}`
    }

    userInput.value = ''
    emit('refresh-history', false)

  } catch (error) {
    if (props.messages[messageIndex]) {
      props.messages.splice(messageIndex, 1)
    }
    ElMessageBox.alert(error.response?.data?.message || '生成视频请求失败，请检查网络或稍后重试', '错误', {
      confirmButtonText: '确定',
      type: 'error',
    })
    isGenerating.value = false
  }
}

const handleKeyPress = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    handleSendMessage()
  }
}

const handleOperationClick = async (operationId, message) => {
  console.log('Operation clicked:', operationId, message)
}
</script>

<template>
  <div class="w-[60.78vw] flex flex-col space-y-3">
    <div class="bg-[#1B2130] border border-[#393B4A] h-[80%] p-4 overflow-hidden">
      <div
        ref="messageContainerRef"
        class="flex flex-col space-y-6 h-full overflow-y-auto scrollbar-hide gap-[1vw]"
        @scroll="handleMainScroll"
      >
        <div v-if="messages.length === 0 && isLoading" class="flex flex-col items-center justify-center h-full text-white">
          <svg class="animate-spin h-10 w-10 mb-4 text-white" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          加载中...
        </div>
        <div v-else-if="messages.length === 0 && !isLoading && !hasMoreItems" class="flex flex-col items-center justify-center h-full text-gray-500">
           暂无数据
        </div>
        <div v-else v-for="(message, index) in messages" :key="index" class="flex flex-col px-[0.3vw]" :id="message.messageId">
          <div class="flex items-center mb-[0.4166vw]">
            <div class="w-[2.5vw] h-[2.5vw] rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex-shrink-0 flex items-center justify-center overflow-hidden">
              <img :src="userStore.getUserData.avatar" alt="AI" class="w-full h-full object-cover" loading="lazy">
            </div>

            <div class="ml-[0.625vw]">
              <div class="flex items-center">
                <span class="text-white font-medium text-[0.9vw]">视频生成</span>
                <span class="text-[#D6F1FF] text-[0.7291vw] ml-[0.4166vw]">{{ message.timestamp }}</span>
              </div>
              <div class="text-[#D6F1FF] text-[0.7291vw]" v-html="formatPrompt(message.prompt)"></div>
            </div>
          </div>

          <div class="relative mb-[1.25vw] mx-[0.9vw] mt-[0.3vw]">
            <div class="w-full rounded-lg overflow-hidden bg-[#273047] px-[3vw] py-[0.4vw]">
              <div v-if="message.status === 'success' && message.videoUrl" class="w-full h-[21.5vw] flex justify-center items-center">
                <video
                  v-if="message.videoUrl"
                  :src="message.videoUrl"
                  class="w-full h-full object-cover rounded"
                  controls
                  controlsList="nodownload"
                  :poster="message.imageUrl"
                ></video>
              </div>
              <img v-else-if="message.status === 'success' && message.imageUrl" :src="message.imageUrl" loading="lazy" class="w-full h-[21.5vw] object-contain rounded">
              <div v-else-if="message.status === 'processing' || message.status === 'queueing'" class="w-full h-[21.5vw] flex flex-col items-center justify-center">
                <img src="@/assets/image/public/loading.gif" alt="生成中" class="w-[8vw] h-[8vw] mb-2" loading="lazy"/>
                <p class="text-white">视频生成中...</p>
              </div>
              <div v-else-if="message.status === 'failed'" class="w-full h-[21.5vw] flex flex-col items-center justify-center">
                <p class="text-red-500">视频生成失败</p>
                <p v-if="message.error" class="text-xs text-gray-400">{{ message.error }}</p>
              </div>
              <div v-else class="w-full h-[21.5vw] flex items-center justify-center flex-col">
              <img src="@/assets/image/public/loading.gif" alt="生成中" class="w-[8vw] h-[8vw] mb-2" loading="lazy"/>
                <p class="text-gray-400">等待视频生成...</p>
              </div>
            </div>

            <div class="absolute -bottom-[2.5vw] right-[1vw] flex space-x-2">
              <div v-for="button in operationButtons" :key="button.id">
                <el-tooltip
                  :content="button.tooltip"
                  placement="top"
                  effect="customized"
                  :show-after="100"
                  :hide-after="10"
                >
                  <img
                    :src="button.icon"
                    :alt="button.tooltip"
                    class="w-[2.0833vw] h-[2.0833vw] cursor-pointer"
                    loading="lazy"
                    @click="handleOperationClick(button.id, message)"
                  >
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>

        <div v-if="isLoading && currentPage > 1 && hasMoreItems" class="flex justify-center items-center py-4 text-gray-400">
          <svg class="animate-spin h-5 w-5 mr-3 text-white" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          正在加载更多...
        </div>
        <div v-if="!hasMoreItems && messages.length > 0 && !isLoading && currentPage > 1" class="text-center py-4 text-sm text-gray-500">
          没有更多内容了
        </div>
      </div>
    </div>
    <div class="bg-[#1B2130] border border-[#393B4A] h-[20%] p-4 relative">
      <textarea
        v-model="userInput"
        class="w-full h-full bg-transparent text-white placeholder-gray-400 focus:outline-none resize-none text-[0.9375vw]"
        placeholder="在此输入提示词..."
        @keydown="handleKeyPress"
        :disabled="isGenerating"
      ></textarea>
      <button
        class="absolute bottom-[1.0416vw] right-[1.0416vw] bg-[#223380] text-white px-[1.25vw] py-[0.2vw] rounded text-[1vw]"
        style="box-shadow: 0px 2px 4px 0px rgba(86,122,214,0.6), inset -1px -1px 0px 0px #3354E8;"
        @click="handleSendMessage"
        :disabled="isGenerating || !userInput.trim()"
        :class="{'opacity-50 cursor-not-allowed': isGenerating || !userInput.trim()}"
      >
        {{ isGenerating ? '生成中...' : `立即生成(消耗${userStore.getVideoPrice ? userStore.getVideoPrice(activeTab) : 0}积分)` }}
      </button>
    </div>
  </div>
</template>

<style scoped>
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
</style>
