:start_line:1
-------
<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import { get } from '@/request.js';
import { ElScrollbar } from 'element-plus';

const selectedType = ref('全部');
const selectedStatus = ref('全部');

const types = ['全部', '图片', '视频', '混剪', '数字人'];
const typeMap = {
  '全部': 0,
  '图片': 1,
  '视频': 2,
  '混剪': 3,
  '数字人': 4
};

const worksItems = ref([]);
const isLoading = ref(false);
const currentPage = ref(1);
const hasMoreItems = ref(true);

const worksContainerRef = ref(null);

const setType = (type) => {
  selectedType.value = type;
  currentPage.value = 1;
  worksItems.value = [];
  hasMoreItems.value = true;
  fetchWorksItems(false);
};

const setStatus = (status) => {
  selectedStatus.value = status;
};

const fetchWorksItems = async (isLoadMore = false) => {
  if (isLoading.value) return;
  if (isLoadMore && !hasMoreItems.value) return;

  isLoading.value = true;

  if (!isLoadMore) {
    currentPage.value = 1;
    worksItems.value = [];
    hasMoreItems.value = true;
  }

  try {
    const typeParam = typeMap[selectedType.value];
    const response = await get(`/api/make/list?page=${currentPage.value}&type=${typeParam}`);
    if (response.code === 200 && response.data && response.data.list) {
      const newItems = response.data.list;
      if (newItems.length > 0) {
        worksItems.value.push(...newItems);
        if (newItems.length < response.data.pagination.pageSize) {
          hasMoreItems.value = false;
        } else {
          currentPage.value++;
        }
      } else {
        hasMoreItems.value = false;
      }
    } else {
      if (!isLoadMore) hasMoreItems.value = false;
    }
  } catch (error) {
    console.error('获取作品列表失败:', error);
    if (!isLoadMore) hasMoreItems.value = false;
  } finally {
    isLoading.value = false;
  }
};

const handleScroll = () => {
  if (!worksContainerRef.value) return;
  const el = worksContainerRef.value;
  if (el.scrollHeight - el.scrollTop - el.clientHeight < 200) {
    if (hasMoreItems.value && !isLoading.value) {
      fetchWorksItems(true);
    }
  }
};

onMounted(() => {
  fetchWorksItems(false);
});
</script>

<template>
  <div class="flex flex-col h-full">
    <div class="w-full mb-[0.6vw]">
      <div class="flex w-full gap-[0.15vw]">
        <div
          v-for="type in types"
          :key="type"
          @click="setType(type)"
          :class="{
            'flex-1 text-center py-[0.55vw] text-[1.4vw] leading-[1.363vw] font-bold cursor-pointer transition-colors': true,
            'text-[#C9DBFF] bg-gradient-to-t from-[#243670] to-[#0C153D] shadow-[0_4px_6px_rgba(36,54,112,0.8)]': selectedType === type,
            'text-[#AAAAAA] bg-gradient-to-t from-[#324161] to-[#0A172B] hover:text-[#C9DBFF]': selectedType !== type
          }"
        >
          {{ type }}
        </div>
      </div>
    </div>

    <div
      ref="worksContainerRef"
      class="flex-1 overflow-y-auto scrollbar-hide px-[0.3vw]"
      @scroll="handleScroll"
    >
      <div v-if="worksItems.length === 0 && isLoading" class="flex flex-col items-center justify-center h-full text-white">
        <svg class="animate-spin h-10 w-10 mb-4 text-white" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        加载中...
      </div>
      <div v-else-if="worksItems.length === 0 && !isLoading && !hasMoreItems" class="flex flex-col items-center justify-center h-full text-gray-500">
        暂无数据
      </div>
      <div v-else class="masonry-container">
        <div
          v-for="item in worksItems"
          :key="item.id"
          class="masonry-item policy-card bg-[#1B2130] border border-[#444444] rounded-[4px] overflow-hidden transition-all"
        >
          <div class="relative">
            <img
              :src="item.creations?.[0]?.cover_url"
              :alt="item.prompt"
              class="w-full object-cover"
              loading="lazy"
              v-if="item.creations?.[0]?.cover_url"
            />
            <div v-else class="w-full h-auto aspect-[4/3] flex items-center justify-center text-white text-xs">
              暂无封面
            </div>
          </div>
        </div>
      </div>

      <div v-if="isLoading && currentPage > 1 && hasMoreItems" class="flex justify-center items-center py-4 text-gray-400">
        <svg class="animate-spin h-5 w-5 mr-3 text-white" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        正在加载更多...
      </div>
      <div v-if="!hasMoreItems && worksItems.length > 0 && !isLoading && currentPage > 1" class="text-center py-4 text-sm text-gray-500">
        没有更多内容了
      </div>
    </div>
  </div>
</template>

<style scoped>
.masonry-container {
  column-count: 4;
  column-gap: 1rem;
  width: 100%;
  padding-top: 1rem;

  @media (max-width: 1200px) {
    column-count: 3;
  }

  @media (max-width: 768px) {
    column-count: 2;
  }

  @media (max-width: 480px) {
    column-count: 1;
  }
}

.masonry-item {
  break-inside: avoid;
  margin-bottom: 1rem;
  display: inline-block;
  box-sizing: border-box;
}

.policy-card {
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  &:hover {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
    border-color: gray;
  }
}

.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #8B94A3 #273047;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #273047;
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #8B94A3;
  border-radius: 10px;
  border: 2px solid #273047;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
