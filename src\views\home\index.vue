<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/user';

const router = useRouter();

const GotoPath = (path, params = {}) => {
  useUserStore().setShowBack(true);
  router.push({ path, query: params });
};
</script>

<template>
  <div class="flex h-full space-x-4">
    <div
      class="w-[18.125vw] flex-shrink-0 bg-[#1B2130] p-5 border border-solid border-[#8B94A3] rounded-[15px] h-[98%]"
    >
      <div class="flex flex-col space-y-6 text-white">
        <!-- 政策平台 -->
        <div class="p-4 rounded-[15px] border border-solid border-[#BADEFF]" style="background: linear-gradient( 127deg, #333D5E 0%, #23283D 100%);">
          <h2 class="text-[1.2vw] font-semibold mb-3">政策平台</h2>
          <div class="flex flex-col space-y-3">
            <button class="bg-[#3C4B6B] hover:bg-[#4A5568] text-white py-2 px-4 rounded-md transition-colors duration-200 font-semibold text-[1.1vw]" @click="GotoPath('/dashboard/policy', { type: 'entre' })">创业政策</button>
            <button class="bg-[#3C4B6B] hover:bg-[#4A5568] text-white py-2 px-4 rounded-md transition-colors duration-200 font-semibold text-[1.1vw]" @click="GotoPath('/dashboard/policy', { type: 'platform' })">平台政策</button>
          </div>
        </div>

        <!-- 知识中心 -->
        <div class="p-4 rounded-[15px] border border-solid border-[#9EF4FF]" style="background: linear-gradient( 127deg, #33495E 0%, #23343D 100%);">
          <h2 class="text-[1.2vw] font-semibold mb-3">知识中心</h2>
          <div class="flex flex-col space-y-3">
            <button class="bg-[#3C5B6B] hover:bg-[#527A8A] text-white py-2 px-4 rounded-md transition-colors duration-200 font-semibold text-[1.1vw]" @click="GotoPath('/dashboard/course', { type: 'free' })">免费课程</button>
            <button class="bg-[#3C5B6B] hover:bg-[#527A8A] text-white py-2 px-4 rounded-md transition-colors duration-200 font-semibold text-[1.1vw]" @click="GotoPath('/dashboard/course', { type: 'advanced' })">进阶课程</button>
          </div>
        </div>

        <!-- 数据管理 -->
        <div class="p-4 rounded-[15px] border border-solid border-[#FCEDB6]" style="background: linear-gradient( 127deg, #5E5133 0%, #3D2E23 100%);">
          <h2 class="text-[1.2vw] font-semibold mb-3">数据管理</h2>
          <div class="flex flex-col space-y-3">
             <button class="bg-[#8A754E] hover:bg-[#A89164] text-white py-2 px-4 rounded-md transition-colors duration-200 font-semibold text-[1.1vw]">数据管理中台</button>
          </div>
        </div>
      </div>
    </div>

    <div
      class="flex flex-col flex-grow "
    >
      <div class="h-[3vw] bg-[#1B2130] flex-shrink-0 mb-4 border border-solid border-[#8B94A3] p-5 flex justify-start items-center">
        <div class="relative">
          <p class="text-white font-bold text-border text-[1.1vw]">行业灵感</p>
          <div class="absolute bottom-[-0.4vh] left-[15%] right-[15%] h-[0.4vh] bg-[#00F7FF]"></div>
        </div>
      </div>

      <div class="flex-grow">
        <!-- 省略瀑布流 -->
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>

</style>