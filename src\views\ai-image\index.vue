<script setup>
import { ref, onMounted } from 'vue'
import { get } from '@/request.js'
import { useUserStore } from '@/store/user'
import LeftPanel from './LeftPanel.vue'
import MiddlePanel from './MiddlePanel.vue'
import RightPanel from './RightPanel.vue'

const userStore = useUserStore()

// 共享状态
const messages = ref([])
const historyItems = ref([])
const isLoading = ref(false)
const hasMoreItems = ref(true)
const currentPage = ref(1)
const ITEMS_PER_PAGE = 10
const activeTab = ref('图片生成') // 固定为图片生成

const apiEndpoints = {
  '图片生成': {
    generate: '/api/text2image/generate',
    status: '/api/text2image/status',
    list: '/api/text2image/list'
  }
}

// 历史记录获取函数
const fetchHistoryItems = async (isLoadMore = false) => {
  if (isLoading.value) return;
  if (isLoadMore && !hasMoreItems.value) return;

  isLoading.value = true;

  if (!isLoadMore) {
    currentPage.value = 1;
    historyItems.value = [];
    messages.value = messages.value.filter(m => !m.isFromHistory);
    hasMoreItems.value = true;
  }

  try {
    const response = await get(`${apiEndpoints[activeTab.value].list}?page=${currentPage.value}`);
    if (response.code === 200 && response.data && response.data.items) {
      const newItems = response.data.items;
      if (newItems.length > 0) {
        if (isLoadMore) {
          historyItems.value.push(...newItems);
        } else {
          historyItems.value = newItems;
        }

        // 将历史记录项添加到消息列表中
        newItems.forEach(item => {
          const historyMessage = {
            type: 'ai-image',
            prompt: item.prompt,
            timestamp: new Date(item.created_at).toLocaleString(),
            status: item.state,
            taskId: item.task_id,
            imageUrl: item.creations?.[0]?.cover_url || item.creations?.[0]?.url || '/assets/image/public/loading.gif',
            isFromHistory: true,
            messageId: `message-${item.task_id}`
          };
          if (!messages.value.some(msg => msg.taskId === item.task_id && msg.isFromHistory)) {
             messages.value.push(historyMessage);
          }
        });

        if (newItems.length < ITEMS_PER_PAGE) {
          hasMoreItems.value = false;
        } else {
          currentPage.value++;
        }
      } else {
        hasMoreItems.value = false;
      }
    } else {
      if (!isLoadMore) hasMoreItems.value = false;
    }
  } catch (error) {
    if (!isLoadMore) hasMoreItems.value = false;
  } finally {
    isLoading.value = false;
  }
};

// 显示历史记录项
const showHistoryItem = (item) => {
  const historyMessage = {
    type: 'ai-image',
    prompt: item.prompt,
    timestamp: new Date(item.created_at).toLocaleString(),
    status: item.state === 'success' ? 'success' : item.state,
    taskId: item.task_id,
    imageUrl: item.creations?.[0]?.cover_url || item.creations?.[0]?.url || '/assets/image/public/loading.gif',
    isFromHistory: true,
    messageId: `message-${item.task_id}`
  };

  const existingMessageIndex = messages.value.findIndex(msg => msg.taskId === item.task_id);
  if (existingMessageIndex === -1) {
    messages.value.push(historyMessage);
  }

  // 滚动到对应的消息
  setTimeout(() => {
    const messageElement = document.getElementById(`message-${item.task_id}`);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, 100);
};

onMounted(() => {
  fetchHistoryItems(false);
  userStore.fetchConfigPrice();
})
</script>

<template>
  <div class="flex h-[98%] gap-x-3 text-white">
    <LeftPanel
      :api-endpoints="apiEndpoints"
      :active-tab="activeTab"
      @refresh-history="fetchHistoryItems"
    />
    <MiddlePanel
      :messages="messages"
      :is-loading="isLoading"
      :has-more-items="hasMoreItems"
      :current-page="currentPage"
      :api-endpoints="apiEndpoints"
      :active-tab="activeTab"
      @refresh-history="fetchHistoryItems"
    />
    <RightPanel
      :history-items="historyItems"
      :is-loading="isLoading"
      :has-more-items="hasMoreItems"
      :current-page="currentPage"
      @load-more="fetchHistoryItems"
      @show-history-item="showHistoryItem"
    />
  </div>
</template>