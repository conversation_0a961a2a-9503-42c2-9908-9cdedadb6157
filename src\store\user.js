import { defineStore } from 'pinia';
import { get, post } from '@/request';
import { useRouter } from 'vue-router';
import Cookies from 'js-cookie';

export const useUserStore = defineStore('user', {
  state: () => ({
    userDataList: {},
    isLoading: false,
    error: null,
    showBack: false,
    tokenExpiresAt: null,
    configPrice: {
      text2video: 10,
      image2video: 20,
      reference2video: 30,
      text2image: 5,
      aiteam: 15,
      storyboard_points: 20,
      digital_points: 25,
      teamwork_points: 30
    },
    isPriceLoading: false,
    priceError: null
  }),
  actions: {
    async fetchUserData() {
      this.isLoading = true;
      this.error = null;
      const router = useRouter();
      try {
        const response = await get('/api/me', {}, { showError: false });

        if (response && response.data) {
             this.userDataList = response.data;
        } else {
             this.userDataList = {};
        }

      } catch (error) {
        this.error = error;
        this.clearUserData();
        router.push('/');
      } finally {
        this.isLoading = false;
      }
    },
    clearUserData() {
      Cookies.remove('access_token');
      this.userDataList = {};
      this.error = null;
      this.tokenExpiresAt = null;
    },
    updateUserData(newData) {
      this.userDataList = {
        ...this.userDataList,
        ...newData
      };
    },
    setShowBack(newShowBack) {
      this.showBack = newShowBack;
    },
    async fetchConfigPrice() {
      this.isPriceLoading = true;
      this.priceError = null;
      try {
        const response = await get('/api/get_config_price', {}, { showError: false });

        if (response && response.code === 200 && response.data) {
          this.configPrice = {
            text2video: response.data.text2video || 0,
            image2video: response.data.image2video || 0,
            reference2video: response.data.reference2video || 0,
            text2image: response.data.text2image || 0,
            aiteam: response.data.aiteam || 0,
            storyboard_points: response.data.storyboard_points || 0,
            digital_points: response.data.digital_points || 0,
            teamwork_points: response.data.teamwork_points || 0
          };
        }
      } catch (error) {
        this.priceError = error;
        console.error('获取配置价格失败:', error);
      } finally {
        this.isPriceLoading = false;
      }
    }
  },
  getters: {
    getUserData: (state) => state.userDataList,
    getIsLoading: (state) => state.isLoading,
    getError: (state) => state.error,
    getShowBack: (state) => state.showBack,
    getConfigPrice: (state) => state.configPrice,
    getIsPriceLoading: (state) => state.isPriceLoading,
    getPriceError: (state) => state.priceError,
    getVideoPrice: (state) => (mode) => {
      const modeMap = {
        '文生视频': 'text2video',
        '图生视频': 'image2video',
        '参考生视频': 'reference2video'
      };
      return state.configPrice[modeMap[mode]] || 0;
    },
    getImagePrice: (state) => state.configPrice.text2image || 0,
    getTeamPrice: (state) => state.configPrice.aiteam || 0,
    getStoryboardPrice: (state) => state.configPrice.storyboard_points || 0,
    getDigitalPrice: (state) => state.configPrice.digital_points || 0,
    getTeamworkPrice: (state) => state.configPrice.teamwork_points || 0
  }
});