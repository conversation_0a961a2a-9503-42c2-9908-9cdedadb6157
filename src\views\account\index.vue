<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useUserStore } from '@/store/user';
import { post, get, del } from '@/request';
import { ElMessage, ElMessageBox, ElImage } from 'element-plus';
import { useRouter } from 'vue-router';

const router = useRouter();
const userStore = useUserStore();
const default_num = ref(3);

const isEditingNickname = ref(false);
const isEditingMobile = ref(false);
const isEditingMaterials = ref(false);

const editingNickname = ref('');
const editingMobile = ref('');
const editingMaterials = ref('');

const passwordDialogVisible = ref(false);
const passwordForm = ref({ oldPassword: '', newPassword: '', confirmPassword: '' });

const userInfo = computed(() => userStore.getUserData);
const userNickname = computed(() => userInfo.value?.nickname || '加载中...');
const userMobile = computed(() => userInfo.value?.mobile || '加载中...');
const userMemberId = computed(() => userInfo.value?.member_id || '加载中...');
const userMaterials = computed(() => userInfo.value?.materials || '');
const userAvatar = computed(() => userInfo.value?.avatar ? userInfo.value.avatar : null);

const personalImages = ref([]);
const selectedFiles = ref([]);
const isUploadingPersonalImage = ref(false);
const personalImageInputRef = ref(null);
const isLoadingImages = ref(false);

const avatarInputRef = ref(null);
const isUploading = ref(false);

const triggerAvatarUpload = () => {
  avatarInputRef.value.click();
};

const handleAvatarUpload = async (event) => {
  const file = event.target.files[0];
  if (!file) return;

  const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];
  if (!validTypes.includes(file.type)) {
    ElMessage.error('请上传 JPG、JPEG 或 PNG 格式的图片');
    return;
  }

  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过5MB');
    return;
  }

  try {
    isUploading.value = true;

    const formData = new FormData();
    formData.append('avatar', file);

    const res = await post('/api/upload/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      showError: true
    });

    if (res.code === 200) {
      ElMessage.success('头像上传成功');
      userStore.updateUserData({
        avatar: res.data.fullPath
      });
    }
  } catch (error) {
    ElMessage.error('头像上传失败，请稍后重试');
    console.error('Avatar upload error:', error);
  } finally {
    isUploading.value = false;
    event.target.value = '';
  }
};

const triggerPersonalImageUpload = () => {
  personalImageInputRef.value.click();
};

const handlePersonalImageSelect = (event) => {
  const files = event.target.files;
  if (!files || files.length === 0) return;

  const validImageTypes = ['image/jpeg', 'image/png', 'image/jpg'];
  const validVideoTypes = ['video/mp4', 'video/quicktime', 'video/x-msvideo'];

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    if (!validImageTypes.includes(file.type) && !validVideoTypes.includes(file.type)) {
      ElMessage.error('请上传有效的图片(JPG、JPEG、PNG)或视频(MP4)文件');
      event.target.value = '';
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      ElMessage.error('文件大小不能超过10MB');
      event.target.value = '';
      return;
    }
  }

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const fileURL = URL.createObjectURL(file);

    selectedFiles.value.push({
      file: file,
      preview: fileURL,
      type: file.type
    });
  }

  event.target.value = '';
};

const submitPersonalImages = async () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择个人形象图片或视频');
    return;
  }

  try {
    isUploadingPersonalImage.value = true;

    const formData = new FormData();
    for (let i = 0; i < selectedFiles.value.length; i++) {
      formData.append(`files[${i}]`, selectedFiles.value[i].file);
    }

    const res = await post('/api/member-info/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      showError: true
    });

    if (res.code === 200) {
      ElMessage.success('个人形象上传成功');
      selectedFiles.value.forEach(item => {
        URL.revokeObjectURL(item.preview);
      });
      selectedFiles.value = [];

      await fetchMemberInfo();
    }
  } catch (error) {
    ElMessage.error('个人形象上传失败，请稍后重试');
    console.error('Personal image upload error:', error);
  } finally {
    isUploadingPersonalImage.value = false;
  }
};

const removeSelectedFile = (index) => {
  URL.revokeObjectURL(selectedFiles.value[index].preview);
  selectedFiles.value.splice(index, 1);
};

watch(userInfo, (newUserInfo) => {
  if (newUserInfo) {
    editingNickname.value = newUserInfo.nickname || '';
    editingMobile.value = newUserInfo.mobile || '';
    editingMaterials.value = newUserInfo.materials || '';
  }
}, { immediate: true, deep: true });

const vipExpiry = computed(() => {
  const vipTime = userInfo.value?.vip_time;
  return vipTime ? vipTime.split('T')[0] : '暂未开通';
});
const userPoints = computed(() => userInfo.value?.points ?? '加载中...');

const handleLogout = async () => {
  await post('/api/logout');
  userStore.clearUserData();
  router.push('/');
};

const toggleEdit = (field) => {
  if (field === 'nickname') {
    isEditingNickname.value = !isEditingNickname.value;
    if (isEditingNickname.value) {
      editingNickname.value = userNickname.value;
    }
  } else if (field === 'mobile') {
    isEditingMobile.value = !isEditingMobile.value;
     if (isEditingMobile.value) {
      editingMobile.value = userMobile.value;
    }
  } else if (field === 'materials') {
    isEditingMaterials.value = !isEditingMaterials.value;
     if (isEditingMaterials.value) {
      editingMaterials.value = userMaterials.value;
    }
  }
};

const openPasswordDialog = () => {
  passwordForm.value.oldPassword = '';
  passwordForm.value.newPassword = '';
  passwordForm.value.confirmPassword = '';
  passwordDialogVisible.value = true;
};

const updateProfile = async () => {
  if (!editingNickname.value) {
    ElMessage.warning('昵称不能为空');
    return;
  }

  var payload = {};
  if (editingNickname.value !== userNickname.value) {
    payload.nickname = editingNickname.value;
  }
  if (editingMobile.value !== userMobile.value) {
    if (editingMobile.value && !/^1[3-9]\d{9}$/.test(editingMobile.value)) {
     ElMessage.warning('请输入正确的手机号');
     return;
   }
    payload.mobile = editingMobile.value;
  }
  if (editingMaterials.value !== userMaterials.value) {
    payload.materials = editingMaterials.value;
  }

  const res = await post('/api/update-profile', payload, {showError: true});
  if (res.code === 200) {
    ElMessage.success('个人资料更新成功');
    var mobileNew = editingMobile.value.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    userStore.updateUserData({
      nickname: editingNickname.value,
      mobile: mobileNew,
      materials: editingMaterials.value
    });
    isEditingNickname.value = false;
    isEditingMobile.value = false;
    isEditingMaterials.value = false;
    router.go(0);
  }
};


const updatePassword = async () => {
  if (!passwordForm.value.oldPassword || !passwordForm.value.newPassword || !passwordForm.value.confirmPassword) {
    ElMessage.warning('请填写完整密码信息');
    return;
  }

  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    ElMessage.warning('两次输入的新密码不一致');
    return;
  }

  if (passwordForm.value.newPassword.length < 6) {
    ElMessage.warning('新密码长度不能少于6位');
    return;
  }

  const res = await post('/api/edit-password', {
    old_password: passwordForm.value.oldPassword,
    new_password: passwordForm.value.newPassword,
    confirm_new_password: passwordForm.value.confirmPassword
  });
  if (res.code == 200) {
    ElMessage.success(res.message);
    passwordDialogVisible.value = false;
    router.push("/");
  }
};

const fetchMemberInfo = async () => {
  try {
    isLoadingImages.value = true;
    const res = await get('/api/member-info/list');
    if (res.code === 200 && res.data) {
      personalImages.value = res.data.map(item => ({
        id: item.id,
        url: item.full_path,
        type: item.type,
        filename: item.filename
      }));
    }
  } catch (error) {
    console.error('Failed to fetch member info:', error);
  } finally {
    isLoadingImages.value = false;
  }
};

const deleteMemberInfo = async (id, index) => {
  try {
    ElMessageBox.confirm('确定要删除这个形象图片/视频吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const res = await del('/api/member-info/delete', { id });
      if (res.code === 200) {
        ElMessage.success('删除成功');
        personalImages.value.splice(index, 1);
      }
    }).catch(() => {
    });
  } catch (error) {
    ElMessage.error('删除失败，请稍后重试');
    console.error('Failed to delete member info:', error);
  }
};

onMounted(() => {
  fetchMemberInfo();
});
</script>

<template>
  <div class="text-white flex flex-col h-full overflow-auto scrollbar-hide">
    <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between mb-4 px-8 py-6 bg-[#1B2130] border border-solid border-[#8B94A3] flex-shrink-0">
      <div class="flex items-center mb-4 lg:mb-0">
        <div class="w-16 h-16 rounded-full bg-[#CCCCCC] mr-4 overflow-hidden">
          <el-image v-if="userAvatar" :src="userAvatar" alt="用户头像" lazy class="w-full h-full object-cover" :preview-src-list="[userAvatar]"/>
        </div>
        <div>
          <h2 class="text-xl font-semibold mb-1">{{ userNickname }}</h2>
          <p class="text-sm text-white mb-1">绑定手机号: {{ userMobile }}</p>
          <p class="text-sm text-white">ID: {{ userMemberId }}</p>
        </div>
      </div>
      <div class="flex flex-wrap gap-8 items-center">
        <div>
          <p class="text-sm text-white text-center">会员到期时间</p>
          <p class="text-lg font-medium text-center">{{ vipExpiry }}</p>
        </div>
        <div>
          <p class="text-sm text-white">剩余积分</p>
          <p class="text-lg font-medium text-center">{{ userPoints }}</p>
        </div>
        <button class="bg-white hover:bg-gray-200 text-black py-1 px-4 rounded-lg font-bold" @click="handleLogout">
          退出账号
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 flex-grow min-h-0 mb-3">
      <div class="lg:col-span-1 bg-[#1B2130] p-6 border border-solid border-[#8B94A3] overflow-auto scrollbar-hide flex flex-col">
        <h3 class="text-lg font-semibold mb-3">账号设置</h3>
        <div class="space-y-[1.5vw]">
          <div class="flex items-center">
            <span class="w-24 text-white">头像</span>
            <div
              class="w-12 h-12 flex items-center justify-center cursor-pointer overflow-hidden rounded-full relative"
              @click="triggerAvatarUpload"
            >
              <el-image
                v-if="userAvatar"
                :src="userAvatar"
                lazy
                alt="用户头像"
                class="w-full h-full object-cover"
              />
              <img
                v-else
                src="@/assets/image/account/user_edit.png"
                alt="上传头像"
                class="w-[100%] h-[100%]"
              />
              <div v-if="isUploading" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                <div class="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              </div>
            </div>
            <input
              ref="avatarInputRef"
              type="file"
              accept=".jpg,.jpeg,.png"
              class="hidden"
              @change="handleAvatarUpload"
            />
          </div>
          <div class="flex items-center">
            <span class="w-24 text-white">ID</span>
            <span>{{ userMemberId }}</span>
          </div>
          <div class="flex items-center">
            <span class="w-24 text-white flex-shrink-0">昵称</span>
            <div class="flex items-center flex-grow">
              <input v-if="isEditingNickname" v-model="editingNickname" class="bg-transparent border border-gray-600 rounded px-2 py-1 text-white focus:outline-none focus:border-blue-500" />
              <span v-else>{{ userNickname }}</span>
              <button class="ml-4 flex-shrink-0" @click="toggleEdit('nickname')"><img src="@/assets/image/account/edit.png" alt="编辑" class="w-4 h-4" loading="lazy"></button>
            </div>
          </div>
          <div class="flex items-center">
            <span class="w-24 text-white flex-shrink-0">绑定手机号</span>
            <div class="flex items-center flex-grow">
              <input v-if="isEditingMobile" v-model="editingMobile" class="bg-transparent border border-gray-600 rounded px-2 py-1 text-white focus:outline-none focus:border-blue-500" />
              <span v-else>{{ userMobile || '未绑定' }}</span>
              <button class="ml-4 flex-shrink-0" @click="toggleEdit('mobile')"><img src="@/assets/image/account/edit.png" alt="编辑" class="w-4 h-4" loading="lazy"></button>
            </div>
          </div>
          <div class="flex items-center">
            <span class="w-24 text-white flex-shrink-0">密码</span>
            <span>******</span>
             <button class="ml-4" @click="openPasswordDialog"><img src="@/assets/image/account/edit.png" alt="编辑" class="w-4 h-4" loading="lazy"></button>
          </div>
        </div>

        <div class="mt-3 pt-3 border-t border-gray-700">
          <h4 class="text-md font-semibold mb-3">平台政策与协议</h4>
          <p class="text-sm text-gray-400 mb-4">
            登录即默认同意 <a href="#" class="text-blue-400">《用户协议》</a> 和 <a href="#" class="text-blue-400">《隐私政策》</a>
          </p>
          <button class="bg-white hover:bg-gray-200 text-black py-1 px-4 rounded-lg font-bold" @click="updateProfile">
            信息提交
          </button>
        </div>

        <div class="mt-3 pt-2 flex flex-col flex-grow">
          <div class="flex items-center mb-3">
            <h4 class="text-md font-semibold">填写个人资料</h4>
          </div>
          <div class="relative flex-grow">
            <textarea
              class="w-full h-full bg-transparent outline-none text-sm text-white placeholder-[#8B94A3] border border-solid border-[#3B3D52] rounded-md p-3 resize-none"
              placeholder="填写您的个人资料..."
              v-model="editingMaterials"
            ></textarea>
            <button class="absolute bottom-4 right-2 bg-white hover:bg-gray-200 text-black py-1 px-4 rounded-lg font-bold z-10" @click="updateProfile">
              资料提交
            </button>
          </div>
        </div>
      </div>

      <div class="relative lg:col-span-1 bg-[#1B2130] p-6 border border-solid border-[#8B94A3] overflow-auto scrollbar-hide">
        <p class="text-sm text-white mb-6">个人形象（上传一张图或个人视频，可以后期和数字人联动）</p>

        <div v-if="isLoadingImages" class="flex justify-center items-center py-8">
          <div class="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
        </div>

        <div v-else class="grid grid-cols-3 gap-4 mb-6">
          <div
            v-for="(item, index) in personalImages"
            :key="`uploaded-${index}`"
            class="aspect-square border-2 border-solid border-gray-600 rounded-lg overflow-hidden relative"
          >
            <el-image v-if="item.type === 'image'" :src="item.url" class="w-full h-full object-cover" lazy :preview-src-list="[item.url]"/>
            <video v-else-if="item.type === 'video'" :src="item.url" class="w-full h-full object-cover" controls></video>

            <button
              class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center"
              @click.stop="deleteMemberInfo(item.id, index)"
            >
              ×
            </button>
          </div>

          <div
            v-for="(item, index) in selectedFiles"
            :key="`selected-${index}`"
            class="aspect-square border-2 border-solid border-blue-600 rounded-lg overflow-hidden relative"
          >
            <el-image v-if="item.type.includes('image')" :src="item.preview" lazy class="w-full h-full object-cover"/>
            <video v-else-if="item.type.includes('video')" :src="item.preview" class="w-full h-full object-cover" controls></video>

            <button
              class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center"
              @click.stop="removeSelectedFile(index)"
            >
              ×
            </button>
          </div>

          <div
            v-if="personalImages.length + selectedFiles.length < default_num"
            class="aspect-square border-2 border-dashed border-gray-600 rounded-lg flex items-center justify-center hover:border-gray-400 cursor-pointer relative"
            @click="triggerPersonalImageUpload"
          >
            <img src="@/assets/image/account/add.png" class="w-[40%] h-[40%]" fit="contain" loading="lazy"/>
          </div>
        </div>

        <input
          ref="personalImageInputRef"
          type="file"
          accept=".jpg,.jpeg,.png,.mp4"
          class="hidden"
          multiple
          @change="handlePersonalImageSelect"
        />

        <button
          class="absolute bottom-4 right-4 bg-white hover:bg-gray-200 text-black py-1 px-4 rounded-lg z-10 font-bold"
          @click="submitPersonalImages"
          :disabled="isUploadingPersonalImage || selectedFiles.length === 0"
        >
          上传提交
        </button>
        <div v-if="isUploadingPersonalImage" class="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
          <div class="w-12 h-12 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    </div>

    <el-dialog
      v-model="passwordDialogVisible"
      title="修改密码"
      width="400px"
      :append-to-body="true"
      destroy-on-close
      header-class="custom-header-dialog"
      body-class="custom-body-dialog"
      footer-class="custom-footer-dialog"
      class="custom-dialog"
    >
      <div class="dialog-content">
        <el-form label-position="top">
          <el-form-item label="当前密码">
            <el-input
              v-model="passwordForm.oldPassword"
              placeholder="请输入当前密码"
              type="password"
              show-password
            />
          </el-form-item>
          <el-form-item label="新密码">
            <el-input
              v-model="passwordForm.newPassword"
              placeholder="请输入新密码"
              type="password"
              show-password
            />
          </el-form-item>
          <el-form-item label="确认新密码">
            <el-input
              v-model="passwordForm.confirmPassword"
              placeholder="请再次输入新密码"
              type="password"
              show-password
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <button class="bg-white hover:bg-gray-200 text-black py-1 px-4 rounded-lg font-bold z-10" @click="updatePassword">
            确认修改
          </button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.scrollbar-hide::-webkit-scrollbar {
    display: none;
}
.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
</style>