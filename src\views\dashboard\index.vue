<script setup>
import { onMounted } from 'vue';
import Sidebar from "./sidebar.vue";
import Top from "./top.vue";
import { RouterView } from "vue-router";
import router from "@/router";
import { useUserStore } from '@/store/user';
import { post } from '@/request';
const userStore = useUserStore();

onMounted(async () => {
  userStore.fetchUserData();

  post('/api/visit').then(res => {
  });
});
</script>

<template>
  <div class="flex h-screen main-container">
    <Sidebar />
    <div class="flex-1 flex flex-col overflow-hidden w-[86.5vw] main-body">
      <Top />
      <div class="flex-1 overflow-y-auto box-border px-[1vw] mt-[0.3vh] scrollbar-hide">
        <RouterView />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.main-container {
  .main-body {
    background: linear-gradient( 90deg, #0A0A0A 0%, rgba(42, 92, 170, 0.5) 100%), #000000;
  }
}
</style>
