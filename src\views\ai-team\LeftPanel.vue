<script setup>
import { ref, onMounted, computed } from 'vue';
import { get, del } from '@/request';
import { ElMessage, ElMessageBox, ElScrollbar } from 'element-plus';

// 左侧面板组件

// Emits
const emit = defineEmits(['new-task', 'load-chat', 'chat-deleted']);

// 响应式数据
const chatHistory = ref([]);
const isLoading = ref(false);
const currentPage = ref(1);
const hasMoreItems = ref(true);
const historyScrollbarRef = ref(null);
const currentLoadedChatId = ref(null); // 跟踪当前加载的聊天记录ID

// 处理新建任务点击
const handleNewTask = () => {
  currentLoadedChatId.value = null;
  emit('new-task');
};

// 格式化日期显示
const formatDateLabel = (dateString) => {
  const now = new Date();
  const date = new Date(dateString);
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
  const targetDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());

  if (targetDate.getTime() === today.getTime()) {
    return '今天';
  } else if (targetDate.getTime() === yesterday.getTime()) {
    return '昨天';
  } else {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
};

// 获取状态显示文本
const getStateText = (state) => {
  return state === 'waiting' ? '闲聊中...' : '已完成';
};

// 获取类型显示文本
const getTypeText = (type) => {
  switch (type) {
    case 'custom':
      return '闲聊状态';
    case 'storyboard_auto':
      return '自动工作';
    case 'storyboard_handmade':
      return '监督工作';
    case 'digital_person':
      return '数字人模式';
    case 'teamwork':
      return '团队协作';
    default:
      return '其他状态';
  }
};

// 获取聊天历史记录
const fetchChatHistory = async (isLoadMore = false) => {
  if (isLoading.value) return;
  if (isLoadMore && !hasMoreItems.value) return;

  isLoading.value = true;

  if (!isLoadMore) {
    currentPage.value = 1;
    chatHistory.value = [];
    hasMoreItems.value = true;
  }

  try {
    const response = await get('/api/chat/generate/history', { page: currentPage.value });
    if (response.code === 200 && response.data && response.data.data) {
      const newItems = response.data.data;
      if (newItems.length > 0) {
        if (isLoadMore) {
          chatHistory.value.push(...newItems);
        } else {
          chatHistory.value = newItems;
        }

        // 检查是否还有更多数据
        const totalPages = Math.ceil(response.data.total / response.data.per_page);
        if (currentPage.value >= totalPages) {
          hasMoreItems.value = false;
        } else {
          currentPage.value++;
        }
      } else {
        hasMoreItems.value = false;
      }
    } else {
      if (!isLoadMore) hasMoreItems.value = false;
    }
  } catch (error) {
    console.error('获取聊天历史失败:', error);
    ElMessage.error('获取聊天历史失败');
    if (!isLoadMore) hasMoreItems.value = false;
  } finally {
    isLoading.value = false;
  }
};

// 无缝刷新聊天历史（不显示加载状态）
const refreshChatHistorySeamlessly = async () => {
  try {
    // 重置分页状态但不清空现有数据
    const tempCurrentPage = currentPage.value;
    const tempHasMoreItems = hasMoreItems.value;

    currentPage.value = 1;
    hasMoreItems.value = true;

    const response = await get('/api/chat/generate/history', { page: 1 });
    if (response.code === 200 && response.data && response.data.data) {
      const newItems = response.data.data;

      // 直接用新数据覆盖原有数据
      chatHistory.value = newItems;

      // 更新分页信息
      if (newItems.length > 0) {
        const totalPages = Math.ceil(response.data.total / response.data.per_page);
        if (1 >= totalPages) {
          hasMoreItems.value = false;
          currentPage.value = 1;
        } else {
          hasMoreItems.value = true;
          currentPage.value = 2; // 下次加载第2页
        }
      } else {
        hasMoreItems.value = false;
        currentPage.value = 1;
      }
    } else {
      // 如果请求失败，恢复原来的分页状态
      currentPage.value = tempCurrentPage;
      hasMoreItems.value = tempHasMoreItems;
    }
  } catch (error) {
    console.error('无缝刷新聊天历史失败:', error);
    // 静默处理错误，不显示错误提示，保持原有数据
  }
};

// 删除聊天记录
const deleteChatHistory = async (chatId) => {
  try {
    await ElMessageBox.confirm('确定要删除这条聊天记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    // 检查是否删除的是当前加载的聊天记录
    const isCurrentChat = currentLoadedChatId.value === chatId;

    const response = await del('/api/chat/generate/delete', { chat_id: chatId });
    if (response.code === 200) {
      ElMessage.success('删除成功');

      // 如果删除的是当前显示的聊天记录，通知父组件重置MiddlePanel
      if (isCurrentChat) {
        currentLoadedChatId.value = null;
        emit('chat-deleted', chatId);
      }

      // 无缝刷新聊天历史，不显示加载状态
      await refreshChatHistorySeamlessly();
    } else {
      ElMessage.error(response.message || '删除失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除聊天记录失败:', error);
      ElMessage.error('删除失败，请稍后重试');
    }
  }
};

// 加载聊天记录
const loadChat = (chatItem) => {
  currentLoadedChatId.value = chatItem.chat_id;
  emit('load-chat', chatItem);
};

// 处理滚动事件
const handleHistoryScroll = ({ scrollTop }) => {
  if (!historyScrollbarRef.value || !historyScrollbarRef.value.wrapRef) return;
  const el = historyScrollbarRef.value.wrapRef;
  if (el.scrollHeight - scrollTop - el.clientHeight < 100) {
    if (hasMoreItems.value && !isLoading.value) {
      fetchChatHistory(true);
    }
  }
};

// 按日期分组聊天记录
const groupedChatHistory = computed(() => {
  const groups = {};
  chatHistory.value.forEach(chat => {
    const dateLabel = formatDateLabel(chat.create_at);
    if (!groups[dateLabel]) {
      groups[dateLabel] = [];
    }
    groups[dateLabel].push(chat);
  });
  return groups;
});

// 组件挂载时获取聊天历史
onMounted(() => {
  fetchChatHistory();
});

// 暴露方法给父组件
defineExpose({
  refreshChatHistorySeamlessly
});
</script>

<template>
  <div class="min-w-[17.625vw] bg-[#1B2130] border border-[#393B4A] p-[0.8333vw] flex flex-col">
    <!-- 新建任务按钮 -->
    <div
      class="w-full h-[4.4vw] bg-[#303A54] border border-[#8B94A3] flex items-center justify-center cursor-pointer hover:bg-[#3A4451] transition-colors duration-200 mb-4 flex-shrink-0"
      @click="handleNewTask"
    >
      <img src="@/assets/image/ai-team/add_task.png" alt="新建任务" class="w-[1.1vw] h-[1.1vw] mr-[0.4vw]" loading="lazy">
      <span class="text-[#C4C4C4] text-[1vw] font-medium">新建任务</span>
    </div>

    <!-- 聊天历史记录 -->
    <div v-if="isLoading && currentPage === 1" class="flex items-center justify-center py-4 flex-grow">
      <span class="text-[#C4C4C4] text-[0.9vw]">加载中...</span>
    </div>

    <el-scrollbar
      v-else
      ref="historyScrollbarRef"
      class="flex-grow h-0 hide-scrollbar"
      @scroll="handleHistoryScroll"
    >
      <div class="space-y-4">
      <!-- 按日期分组显示聊天历史 -->
      <div v-for="(chats, dateLabel) in groupedChatHistory" :key="dateLabel" class="space-y-2">
        <!-- 日期标题 -->
        <div class="text-[#C4C4C4] text-[0.9vw] font-medium px-2">
          {{ dateLabel }}
        </div>

        <!-- 该日期下的聊天记录 -->
        <div v-for="chat in chats" :key="chat.id" class="space-y-1">
          <!-- 聊天卡片 -->
          <div
            class="bg-[#303A54] border border-[#393B4A] py-[0.3vw] px-[1vw] cursor-pointer hover:bg-[#3A4451] transition-colors duration-200"
            @click="loadChat(chat)"
          >
            <!-- 标题 -->
            <div class="text-[#FFFFFF] text-[0.85vw] font-[400] mb-[0.3vw] truncate">
              {{ chat.title }}
            </div>

            <!-- 底部信息行 -->
            <div class="flex items-center justify-between">
              <!-- 左侧状态信息 -->
              <div class="flex items-center">
                <span class="text-[#C4C4C4] text-[0.8vw]">
                  {{ getStateText(chat.state) }}
                </span>
              </div>

              <!-- 右侧类型和删除按钮 -->
              <div class="flex items-center space-x-2">
                <span class="text-[#C4C4C4] text-[0.8vw] px-[0.35vw] py-[0.15vw] bg-[#303A54] border border-[#8B94A3] rounded">
                  {{ getTypeText(chat.type) }}
                </span>
                <div
                  class="w-[1.3vw] h-[1.3vw] flex items-center justify-center cursor-pointer hover:opacity-80 transition-opacity duration-200"
                  @click.stop="deleteChatHistory(chat.chat_id)"
                >
                  <img
                    src="@/assets/image/ai-team/delete.png"
                    alt="删除"
                    class="w-full h-full object-contain"
                    loading="lazy"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

        <!-- 空状态 -->
        <div v-if="Object.keys(groupedChatHistory).length === 0" class="text-center py-8">
          <span class="text-[#8B94A3] text-[0.9vw]">暂无聊天记录</span>
        </div>
      </div>

      <!-- 加载更多状态 -->
      <div v-if="isLoading && currentPage > 1 && hasMoreItems" class="flex justify-center items-center py-2">
        <svg class="animate-spin h-5 w-5 text-white" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>

      <!-- 没有更多数据提示 -->
      <div v-if="!hasMoreItems && chatHistory.length > 0 && !isLoading && currentPage > 1" class="text-center py-2 text-[0.7vw] text-[#8B94A3]">
        没有更多了
      </div>
    </el-scrollbar>
  </div>
</template>

<style scoped>
/* 隐藏 ElScrollbar 的滚动条 */
.hide-scrollbar :deep(.el-scrollbar__bar) {
  display: none !important;
}

.hide-scrollbar :deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}
</style>
