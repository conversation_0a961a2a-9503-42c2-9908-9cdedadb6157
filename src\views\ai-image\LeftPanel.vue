<script setup>
import { ref } from 'vue'
import { <PERSON><PERSON><PERSON><PERSON>, ElSlider } from 'element-plus'
import addImagePlaceholderIcon from '@/assets/image/ai-video/upload.png'

// Props
const props = defineProps({
  apiEndpoints: Object,
  activeTab: String
})

// Emits
const emit = defineEmits(['refresh-history'])

// 状态
const selectedImageRatio = ref('1:1')
const referenceImages = ref([null, null, null])
const fileInputs = ref([])
const referenceStrength = ref(100)
const selectedReferenceTab = ref('形象特征')
const selectedStyleCategory = ref('推荐')
const selectedStyleName = ref(null)

// 数据
const imageRatios = ref([
  { ratio: '1:1', text: '头像框' },
  { ratio: '16:9', text: '电脑壁纸' },
  { ratio: '9:16', text: '手机配图' },
  { ratio: '3:2', text: '模板名片' },
  { ratio: '3:4', text: '媒体配图' },
  { ratio: '2:3', text: '小红书图' }
])

const referenceTabs = ref(['形象特征', '人物长相'])
const styleCategories = ref(['推荐', '男生', '女生', '动漫风', '国风', '二次元', '场景'])
const styles = ref([
  { name: '卡通人物', image: 'https://picsum.photos/80/120?random=1', category: '推荐' },
  { name: '动漫人物', image: 'https://picsum.photos/80/120?random=2', category: '推荐' },
  { name: '写实人物', image: 'https://picsum.photos/80/120?random=3', category: '推荐' },
  { name: '东方美女', image: 'https://picsum.photos/80/120?random=4', category: '推荐' },
  { name: '插画人物', image: 'https://picsum.photos/80/120?random=5', category: '推荐' },
  { name: '商业插画', image: 'https://picsum.photos/80/120?random=6', category: '推荐' },
  { name: '玫瑰花海', image: 'https://picsum.photos/80/120?random=7', category: '推荐' },
  { name: '水彩插画', image: 'https://picsum.photos/80/120?random=8', category: '推荐' },
  { name: '恐怖悬疑', image: 'https://picsum.photos/80/120?random=9', category: '推荐' },
  { name: '涂鸦彩绘', image: 'https://picsum.photos/80/120?random=10', category: '推荐' }
])

// 方法
const triggerFileInput = (index) => {
  fileInputs.value[index].click()
}

const handleFileChange = (event, index) => {
  const file = event.target.files[0]
  if (file) {
    const validTypes = ['image/png', 'image/jpeg', 'image/jpg']
    if (validTypes.includes(file.type)) {
      const reader = new FileReader()
      reader.onload = (e) => {
        referenceImages.value[index] = e.target.result
      }
      reader.readAsDataURL(file)
    } else {
      alert('请上传 JPG、JPEG 或 PNG 格式的图片')
      event.target.value = null
    }
  }
}

const removeImage = (index) => {
  referenceImages.value[index] = null
  if (fileInputs.value[index]) {
    fileInputs.value[index].value = null
  }
}

// 暴露给父组件的数据
defineExpose({
  selectedImageRatio,
  referenceImages,
  selectedReferenceTab,
  referenceStrength,
  selectedStyleName
})
</script>

<template>
  <div class="w-[27.28vw] bg-[#1B2130] border border-[#393B4A] p-[0.8333vw] space-y-3 overflow-y-auto text-white overflow-hidden scrollbar-hide">
    <div>
      <h3 class="text-[0.9vw] mb-2 ml-2 font-['AppleSystemUIFont']">图片比例选择</h3>
      <div class="grid grid-cols-3 gap-[6px]">
        <div
          v-for="item in imageRatios"
          :key="item.ratio"
          class="p-[0.6166vw] cursor-pointer text-center border font-['AppleSystemUIFont'] flex flex-col items-center h-[5.2vw] pb-[0.2083vw]"
          :class="selectedImageRatio === item.ratio ? 'bg-[#303A54] border-[#8B94A3]' : 'bg-[#273047] border-[#393B4A]'"
          @click="selectedImageRatio = item.ratio"
        >
          <div class="h-[1.6666vw] flex items-center justify-center mb-[0.2083vw]">
            <div
              class="rounded-md border bg-[#273047]"
              :class="[
                {
                  'w-[1.6vw] h-[1.6vw]': item.ratio === '1:1',
                  'w-[2.9vw] h-[1.6vw]': item.ratio === '16:9',
                  'w-[1.4vw] h-[2.3vw]': item.ratio === '9:16',
                  'w-[1.6vw] h-[2.2vw]': item.ratio === '3:4',
                  'w-[1.9vw] h-[1.34vw]': item.ratio === '3:2',
                  'w-[1.34vw] h-[1.9vw]': item.ratio === '2:3'
                },
                selectedImageRatio === item.ratio ? 'bg-[#424E6E]' : ''
              ]"
            ></div>
          </div>
          <div class="flex-grow"></div>
          <div class="text-[0.8vw] leading-tight" :class="selectedImageRatio === item.ratio ? 'text-white' : 'text-[#C4C4C4]'">{{ item.ratio }}</div>
          <div class="text-[0.8vw] leading-tight" :class="selectedImageRatio === item.ratio ? 'text-white' : 'text-[#C4C4C4]'">{{ item.text }}</div>
        </div>
      </div>
    </div>

    <div>
      <h3 class="text-[0.9vw] mb-1 ml-2 font-['AppleSystemUIFont']">上传参考图</h3>
      <div class="flex space-x-2 mb-2">
        <button
          v-for="tab in referenceTabs"
          :key="tab"
          @click="selectedReferenceTab = tab"
          :class="[
            'px-[1.2vw] py-[0.3083vw] text-[0.8vw] rounded-md font-[\'AppleSystemUIFont\'] transition-colors duration-150 ease-in-out',
            selectedReferenceTab === tab ? 'bg-[#303A54] text-white border border-[#8B94A3]' : 'bg-[#273047] text-[#C4C4C4] border border-transparent hover:bg-[#303A54]/90'
          ]"
        >
          {{ tab }}
        </button>
      </div>

      <div class="mb-2">
        <div class="flex items-stretch gap-[6px]">
          <el-tooltip
            placement="right"
            :offset="-100"
            popper-class="custom-add-image"
            trigger="hover"
            effect="customadd"
            :disabled="!!referenceImages[0]"
          >
            <template #default>
              <div
                class="w-full h-[16vw] bg-[#303A54] border border-[#8B94A3] flex flex-col relative"
                @click="!referenceImages[0] && triggerFileInput(0)"
              >
                <template v-if="referenceImages[0]">
                  <div class="h-[70%] w-full relative overflow-hidden">
                    <div class="blur-bg absolute inset-0" :style="{ backgroundImage: `url(${referenceImages[0]})` }"></div>
                    <div class="absolute inset-0 flex items-center justify-center">
                      <img :src="referenceImages[0]" alt="Reference Image" class="max-h-full max-w-full object-contain z-10" loading="lazy"/>
                    </div>
                  </div>

                  <div class="h-[30%] w-full flex flex-col">
                    <div class="w-full bg-[#273047] p-2 flex flex-col justify-center h-1/2">
                      <div class="flex items-center justify-between">
                        <span class="text-[0.85vw] text-[#C4C4C4] font-400">参考图强度（0-1）</span>
                      </div>
                    </div>
                    <div class="w-full bg-[#273047] p-2 flex flex-col justify-center h-1/2 ">
                      <div class="flex items-center w-full">
                        <span class="text-[1vw] text-[#C4C4C4] mr-2">0</span>
                        <el-slider
                            v-model="referenceStrength"
                            :min="0"
                            :max="1"
                            style="height: 2px"
                            size="small"
                            :step="0.01"
                            class="flex-1 mx-1"
                            tooltip-class="custom-tooltip"
                          />
                        <span class="text-[1vw] text-[#C4C4C4] ml-2">1</span>
                        <div class="ml-3 px-2 bg-[#1B2130] border border-[#8B94A3] flex items-center justify-center text-white text-[1vw] w-[4vw]">{{ referenceStrength }}</div>
                      </div>
                    </div>
                  </div>

                  <button
                    @click.stop="removeImage(0)"
                    class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs z-20">
                    ×
                  </button>
                </template>

                <template v-else>
                  <div class="flex flex-col items-center justify-center h-full cursor-pointer">
                    <img :src="addImagePlaceholderIcon" alt="Upload" class="w-[2.1vw] h-[2.1vw] mb-[0.2123vw] opacity-70" loading="lazy">
                    <p class="text-[0.8vw] text-white font-medium">上传 [{{ selectedReferenceTab }}] 参考图</p>
                    <p class="text-[0.7vw] text-[#C4C4C4] font-sans">支持 JPG / PNG 格式</p>
                  </div>
                </template>

                <input
                  type="file"
                  :ref="el => fileInputs[0] = el"
                  @change="event => handleFileChange(event, 0)"
                  accept=".png,.jpg,.jpeg"
                  class="hidden"
                />
              </div>
            </template>
            <template #content>
              <div @click="triggerFileInput(0)" class="text-[0.75vw] leading-tight text-[#C4C4C4] py-[0.1vw] px-[0.5vw] cursor-pointer hover:text-white">从本地上传</div>
              <div class="text-[0.75vw] leading-tight text-[#C4C4C4] py-[0.1vw] px-[0.5vw] cursor-pointer mt-1 hover:text-white">作品库上传</div>
            </template>
          </el-tooltip>
        </div>
      </div>
    </div>

    <div>
      <h3 class="text-[0.9vw] mb-1 ml-2 font-['AppleSystemUIFont']">风格选择</h3>
      <div class="flex space-x-2 mb-1 overflow-x-auto pb-2 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 justify-center">
        <button
          v-for="category in styleCategories"
          :key="category"
          @click="selectedStyleCategory = category"
          :class="[
            'px-[0.3vw] text-[0.8vw] rounded-md font-[\'AppleSystemUIFont\'] whitespace-nowrap transition-colors duration-150 ease-in-out',
            selectedStyleCategory === category ? 'text-[#00F7FF] text-[0.9vw]' : 'text-white'
          ]"
        >
          {{ category }}
        </button>
      </div>
      <div class="grid grid-cols-5 gap-2">
        <div
          v-for="style in styles" :key="style.name"
          @click="selectedStyleName = style.name"
          :class="[
            'relative overflow-hidden cursor-pointer aspect-square border-2 group transition-all duration-150 ease-in-out',
            selectedStyleName === style.name ? 'border-[#4A90E2]' : 'border-transparent hover:border-[#4A90E2]/50 transform hover:scale-105'
          ]"
        >
          <img loading="lazy" :src="style.image" class="w-full h-full object-cover group-hover:opacity-80 transition-opacity">
          <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-60 py-[0.1vw] px-[0.1vw] text-white text-center text-[0.7vw] leading-tight">
            {{ style.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.reference-upload-container {
  position: relative;
  background: #273047;
}

.blur-bg {
  filter: blur(18px);
  background-size: cover;
  background-position: center;
  width: 100%;
  height: 100%;
  opacity: 0.7;
}

:deep(.custom-add-image) {
  background-color: #1B2130 !important;
  border: 1px solid #474D59 !important;
  padding: 0 !important;
}

:deep(.custom-add-image .el-popper__arrow::before) {
  background-color: #1B2130 !important;
  border-color: #474D59 !important;
}

:deep(.el-slider) {
  --el-slider-main-bg-color: #2B428C;
  --el-slider-height: 4px;
}

:deep(.el-slider__runway) {
  height: 2px !important;
  background-color: #393B4A;
}

:deep(.el-slider__bar) {
  height: 4px !important;
  background-color: #FFFFFF;
}

:deep(.el-slider__button) {
  width: 12px;
  height: 12px;
  background-color: #2B428C;
  border: 2px solid white;
}

:deep(.el-tooltip__popper) {
  background-color: #2B428C !important;
  border-color: #8B94A3 !important;
}

:deep(.el-popper__arrow::before) {
  background-color: #2B428C !important;
  border-color: #8B94A3 !important;
}
</style>
