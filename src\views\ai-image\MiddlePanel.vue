<script setup>
import { ref, watch, nextTick, getCurrentInstance } from 'vue'
import { ElMessageBox, ElTooltip } from 'element-plus'
import { post, get, del } from '@/request.js'
import { useUserStore } from '@/store/user'

import shareIcon from '@/assets/image/ai-video/share.png'
import deleteIcon from '@/assets/image/ai-video/delete.png'
import downloadIcon from '@/assets/image/ai-video/download.png'
import timeIcon from '@/assets/image/ai-video/time.png'

const userStore = useUserStore()

// Props
const props = defineProps({
  messages: Array,
  isLoading: Boolean,
  hasMoreItems: Boolean,
  currentPage: Number,
  apiEndpoints: Object,
  activeTab: String
})

// Emits
const emit = defineEmits(['refresh-history'])

// 状态
const userInput = ref('')
const isGenerating = ref(false)
const messageContainerRef = ref(null)

// 操作按钮
const operationButtons = ref([
  { id: 'share', icon: shareIcon, tooltip: '分享' },
  { id: 'delete', icon: deleteIcon, tooltip: '删除' },
  { id: 'download', icon: downloadIcon, tooltip: '下载' },
  { id: 'time', icon: timeIcon, tooltip: '延时' }
])

// 方法
const formatDateTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

const handleMainScroll = () => {
  if (!messageContainerRef.value) return
  const el = messageContainerRef.value
  if (el.scrollHeight - el.scrollTop - el.clientHeight < 200) {
    if (props.hasMoreItems && !props.isLoading) {
      emit('refresh-history', true)
    }
  }
}

const escapeHtml = (unsafe) => {
  if (typeof unsafe !== 'string') return ''
  return unsafe
    .replace(/&/g, "&")
    .replace(/</g, "<")
    .replace(/>/g, ">")
    .replace(/"/g, "\"")
    .replace(/'/g, "&#039;")
}

const getPromptDisplayParts = (messagePrompt) => {
  return { main: messagePrompt, highlight: null }
}

const formatPrompt = (messagePrompt) => {
  if (!messagePrompt) return ''
  const parts = getPromptDisplayParts(messagePrompt)

  const mainHtml = escapeHtml(parts.main)
  let html = `<span>${mainHtml}</span>`

  if (parts.highlight) {
    const highlightHtml = escapeHtml(parts.highlight)
    const style = `background-color: #171A21; border: 1px solid #232932; color: #D6F1FF; padding: 1px 5px; margin-inline-start: ${parts.main && parts.main.trim() !== '' ? '4px' : '0'};`
    html = parts.main && parts.main.trim() !== '' ? `<span>${mainHtml}</span>` : ''
    html += `<span style="${style}">${highlightHtml}</span>`
  } else {
    html = `<span>${mainHtml}</span>`
  }
  return html
}

const pollTaskStatus = async (taskId, messageIndex) => {
  try {
    const data = await get(`${props.apiEndpoints[props.activeTab].status}/${taskId}`)

    const messageToUpdate = props.messages[messageIndex]

    if (data.state === 'success') {
      if (messageToUpdate) {
        messageToUpdate.imageUrl = data.creations?.[0]?.cover_url || data.creations?.[0]?.url
        messageToUpdate.status = 'success'
      }
      isGenerating.value = false
    } else if (data.state === 'failed') {
      if (messageToUpdate) {
        messageToUpdate.status = 'failed'
      }
      ElMessageBox.alert('任务制作失败，积分已退还', '提示', {
        confirmButtonText: '确定',
        type: 'error',
      })
      isGenerating.value = false
    } else if (['submitted', 'processing'].includes(data.state)) {
      if (messageToUpdate) {
        messageToUpdate.status = data.state
      }
      setTimeout(() => pollTaskStatus(taskId, messageIndex), 5000)
    } else {
      if (messageToUpdate) {
        messageToUpdate.status = 'unknown'
      }
      isGenerating.value = false
    }
  } catch (error) {
    const messageToUpdate = props.messages[messageIndex]
    if (messageToUpdate) {
      messageToUpdate.status = 'failed'
    }
    ElMessageBox.alert('任务状态查询失败，请稍后重试', '错误', {
      confirmButtonText: '确定',
      type: 'error',
    })
    isGenerating.value = false
  }
}

const handleSendMessage = async () => {
  if (!userInput.value.trim() || isGenerating.value) return

  // 暂时跳过参考图片验证，这个需要通过父组件传递
  isGenerating.value = true

  const userPrompt = userInput.value
  let payload = {
    prompt: userPrompt,
    aspect_ratio: '1:1' // 默认比例
  }

  const newMessage = {
    type: 'ai-image',
    prompt: userPrompt,
    timestamp: formatDateTime(),
    status: 'initiating',
    taskId: null,
    imageUrl: '',
    isFromHistory: false,
    messageId: `message-${Date.now()}`
  }

  // 直接修改 messages 数组
  const messageIndex = props.messages.length
  props.messages.push(newMessage)

  await nextTick()
  if (messageContainerRef.value) {
    messageContainerRef.value.scrollTop = 0
  }

  try {
    const data = await post(props.apiEndpoints[props.activeTab].generate, payload, { showError: false })

    if (data.code !== 200) {
      if (props.messages[messageIndex]) {
        props.messages.splice(messageIndex, 1)
      }
      ElMessageBox.alert(data.message || '任务创建失败', '错误', {
        confirmButtonText: '确定',
        type: 'error',
      })
      isGenerating.value = false
      return
    }

    const taskId = data.task_id
    if (props.messages[messageIndex]) {
      props.messages[messageIndex].taskId = taskId
      props.messages[messageIndex].status = 'processing'
      props.messages[messageIndex].messageId = `message-${taskId}`
    }
    setTimeout(() => pollTaskStatus(taskId, messageIndex), 5000)
    userInput.value = ''
    emit('refresh-history', false)

  } catch (error) {
    if (props.messages[messageIndex]) {
      props.messages.splice(messageIndex, 1)
    }
    ElMessageBox.alert(error.response?.data?.message || '生成图片请求失败，请检查网络或稍后重试', '错误', {
      confirmButtonText: '确定',
      type: 'error',
    })
    isGenerating.value = false
  }
}

const handleKeyPress = (event) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    handleSendMessage()
  }
}

const handleOperationClick = async (operationId, message) => {
  // 操作处理逻辑
  console.log('Operation clicked:', operationId, message)
}

// 监听消息变化
watch(() => props.messages, (newMessages) => {
  newMessages.forEach((message, index) => {
    if (message.taskId && ['processing', 'queueing'].includes(message.status)) {
      pollTaskStatus(message.taskId, index)
    }
  })
}, { deep: true })
</script>

<template>
  <div class="w-[60.78vw] flex flex-col space-y-3">
    <div class="bg-[#1B2130] border border-[#393B4A] h-[80%] p-4 overflow-hidden">
      <div
        ref="messageContainerRef"
        class="flex flex-col space-y-6 h-full overflow-y-auto scrollbar-hide gap-[1vw]"
        @scroll="handleMainScroll"
      >
        <div v-if="messages.length === 0 && isLoading" class="flex flex-col items-center justify-center h-full text-white">
          <svg class="animate-spin h-10 w-10 mb-4 text-white" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          加载中...
        </div>
        <div v-else-if="messages.length === 0 && !isLoading && !hasMoreItems" class="flex flex-col items-center justify-center h-full text-gray-500">
           暂无数据
        </div>
        <div v-else v-for="(message, index) in messages" :key="index" class="flex flex-col px-[0.3vw]" :id="message.messageId">
          <div class="flex items-center mb-[0.4166vw]">
            <div class="w-[2.5vw] h-[2.5vw] rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex-shrink-0 flex items-center justify-center overflow-hidden">
              <img :src="userStore.getUserData.avatar" alt="AI" class="w-full h-full object-cover" loading="lazy">
            </div>

            <div class="ml-[0.625vw]">
              <div class="flex items-center">
                <span class="text-white font-medium text-[0.9vw]">图片生成</span>
                <span class="text-[#D6F1FF] text-[0.7291vw] ml-[0.4166vw]">{{ message.timestamp }}</span>
              </div>
              <div class="text-[#D6F1FF] text-[0.7291vw]" v-html="formatPrompt(message.prompt)"></div>
            </div>
          </div>

          <div class="relative mb-[1.25vw] mx-[0.9vw] mt-[0.3vw]">
            <div class="w-full rounded-lg overflow-hidden bg-[#273047] px-[3vw] py-[0.4vw]">
              <div v-if="message.status === 'success' && message.imageUrl" class="w-full h-[21.5vw] flex justify-center items-center">
                <img
                  :src="message.imageUrl"
                  class="w-full h-full object-contain rounded"
                  alt="生成图片"
                  loading="lazy"
                />
              </div>
              <div v-else-if="message.status === 'processing' || message.status === 'queueing'" class="w-full h-[21.5vw] flex flex-col items-center justify-center">
                <img src="@/assets/image/public/loading.gif" alt="生成中" class="w-[8vw] h-[8vw] mb-2" loading="lazy"/>
                <p class="text-white">图片生成中...</p>
              </div>
              <div v-else-if="message.status === 'failed'" class="w-full h-[21.5vw] flex flex-col items-center justify-center">
                <p class="text-red-500">图片生成失败</p>
                <p v-if="message.error" class="text-xs text-gray-400">{{ message.error }}</p>
              </div>
              <div v-else class="w-full h-[21.5vw] flex items-center justify-center flex-col">
              <img src="@/assets/image/public/loading.gif" alt="生成中" class="w-[8vw] h-[8vw] mb-2" loading="lazy"/>
                <p class="text-gray-400">等待图片生成...</p>
              </div>
            </div>

            <div class="absolute -bottom-[2.5vw] right-[1vw] flex space-x-2">
              <div v-for="button in operationButtons" :key="button.id">
                <el-tooltip
                  :content="button.tooltip"
                  placement="top"
                  effect="customized"
                  :show-after="100"
                  :hide-after="10"
                >
                  <img
                    :src="button.icon"
                    :alt="button.tooltip"
                    class="w-[2.0833vw] h-[2.0833vw] cursor-pointer"
                    loading="lazy"
                    @click="handleOperationClick(button.id, message)"
                  >
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>

        <div v-if="isLoading && currentPage > 1 && hasMoreItems" class="flex justify-center items-center py-4 text-gray-400">
          <svg class="animate-spin h-5 w-5 mr-3 text-white" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          正在加载更多...
        </div>
        <div v-if="!hasMoreItems && messages.length > 0 && !isLoading && currentPage > 1" class="text-center py-4 text-sm text-gray-500">
          没有更多内容了
        </div>
      </div>
    </div>
    <div class="bg-[#1B2130] border border-[#393B4A] h-[20%] p-4 relative">
      <textarea
        v-model="userInput"
        class="w-full h-full bg-transparent text-white placeholder-gray-400 focus:outline-none resize-none text-[0.9375vw]"
        placeholder="描述想要生成的内容"
        @keydown="handleKeyPress"
        :disabled="isGenerating"
      ></textarea>
      <button
        class="absolute bottom-[1.0416vw] right-[1.0416vw] bg-[#223380] text-white px-[1.25vw] py-[0.2vw] rounded text-[1vw]"
        style="box-shadow: 0px 2px 4px 0px rgba(86,122,214,0.6), inset -1px -1px 0px 0px #3354E8;"
        @click="handleSendMessage"
        :disabled="isGenerating || !userInput.trim()"
        :class="{'opacity-50 cursor-not-allowed': isGenerating || !userInput.trim()}"
      >
        {{ isGenerating ? '生成中...' : `立即生成(消耗${userStore.getImagePrice}积分)` }}
      </button>
    </div>
  </div>
</template>

<style scoped>
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.loading-dots {
  display: flex;
  align-items: center;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  margin: 0 2px;
  background-color: #D6F1FF;
  border-radius: 50%;
  display: inline-block;
  animation: loading 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1.0);
  }
}
</style>
