import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/store/user'

const routes = [
  {
    path: '/',
    name: 'index',
    component: () => import('@/views/index/index.vue'),
  },
  {
    path: '/dashboard',
    component: () => import('@/views/dashboard/index.vue'),
    children: [
      {
        path: '',
        redirect: { name: 'home' }
      },
      {
        path: 'home',
        name: 'home',
        component: () => import('@/views/home/<USER>'),
      },
      {
        path: 'account',
        name: 'account',
        component: () => import('@/views/account/index.vue'),
      },
      {
        path: 'course',
        name: 'course',
        component: () => import('@/views/course/index.vue'),
      },
      {
        path: 'policy',
        name: 'policy',
        component: () => import('@/views/policy/index.vue'),
      },
      {
        path: 'inspiration',
        name: 'inspiration',
        component: () => import('@/views/inspiration/index.vue'),
      },
      {
        path: 'works',
        name: 'works',
        component: () => import('@/views/works/index.vue'),
      },
      {
        path: 'ai-image',
        name: 'ai-image',
        component: () => import('@/views/ai-image/index.vue'),
      },
      {
        path: 'ai-video',
        name: 'ai-video',
        component: () => import('@/views/ai-video/index.vue'),
      },
      {
        path: 'ai-team',
        name: 'ai-team',
        component: () => import('@/views/ai-team/index.vue'),
      }
    ],
  },
]

const router = createRouter({
  history: createWebHistory("web"),
  routes,
})

export default router
