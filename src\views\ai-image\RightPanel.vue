<script setup>
import { ref } from 'vue'
import { ElScrollbar } from 'element-plus'

// Props
const props = defineProps({
  historyItems: Array,
  isLoading: Boolean,
  hasMoreItems: Boolean,
  currentPage: Number
})

// Emits
const emit = defineEmits(['load-more', 'show-history-item'])

// Refs
const historyScrollbarRef = ref(null)

// 方法
const handleHistoryScroll = ({ scrollTop }) => {
  if (!historyScrollbarRef.value || !historyScrollbarRef.value.wrapRef) return
  const el = historyScrollbarRef.value.wrapRef
  if (el.scrollHeight - scrollTop - el.clientHeight < 100) {
    if (props.hasMoreItems && !props.isLoading) {
      emit('load-more', true)
    }
  }
}

const showHistoryItem = (item) => {
  emit('show-history-item', item)
}
</script>

<template>
  <div class="w-[9.99vw] bg-[#1B2130] border border-[#393B4A] p-[0.83333vw] flex flex-col space-y-3 rounded-[15px]">
    <h3 class="text-[0.9375vw] text-white font-['AppleSystemUIFont'] shrink-0 text-center">历史记录</h3>
    <el-scrollbar
      ref="historyScrollbarRef"
      class="flex-grow h-0"
      @scroll="handleHistoryScroll"
    >
      <div class="space-y-2">
        <div
          v-for="item in historyItems"
          :key="item.id"
          @click="showHistoryItem(item)"
          class="w-full bg-[#273047] rounded overflow-hidden cursor-pointer hover:bg-[#2F3A54] transition-colors"
        >
          <template v-if="['created', 'queueing', 'processing'].includes(item.state)">
            <div class="w-full h-auto aspect-[4/3] flex items-center justify-center text-white text-xs">
              正在生成中...
            </div>
          </template>
          <template v-else>
            <img
              :src="item.creations?.[0]?.cover_url"
              class="w-full h-auto object-cover aspect-[4/3]"
              alt="历史记录封面"
              loading="lazy"
              v-if="item.creations?.[0]?.cover_url"
            />
            <div v-else class="w-full h-auto aspect-[4/3]"></div>
          </template>
        </div>
      </div>

      <div v-if="isLoading && currentPage > 1 && hasMoreItems" class="flex justify-center items-center py-2">
        <svg class="animate-spin h-5 w-5 text-white" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
      <div v-if="!hasMoreItems && historyItems.length > 0 && !isLoading && currentPage > 1" class="text-center py-2 text-xs text-gray-500">
        没有更多了
      </div>
    </el-scrollbar>
  </div>
</template>

<style scoped>
</style>
