<script setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

const menuGroups = ref([
  {
    title: '核心功能',
    items: [
      { name: '首页', icon: 'home', path: '/dashboard/home' },
      { name: '灵感广场', icon: 'inspiration', path: '/dashboard/inspiration' },
      { name: '作品库', icon: 'works', path: '/dashboard/works' },
      { name: '账号管理', icon: 'account', path: '/dashboard/account' },
    ]
  },
  {
    title: '创作工具',
    items: [
      { name: 'AI员工团队', icon: 'team', path: '/dashboard/ai-team' },
      { name: 'AI生图', icon: 'image-gen', path: '/dashboard/ai-image' },
      { name: 'AI视频', icon: 'video', path: '/dashboard/ai-video' },
      { name: 'AI数字人', icon: 'digital-human', path: '/ai-digital-human' },
      { name: 'AI混剪', icon: 'mix-cut', path: '/ai-mix-cut' },
    ]
  },
  {
    title: '分发管理',
    items: [
      { name: '账号运营管理', icon: 'operation', path: '/account-operation' },
      { name: '矩阵分发授权', icon: 'matrix-auth', path: '/matrix-auth' },
      { name: '矩阵发布', icon: 'matrix-publish', path: '/matrix-publish' },
    ]
  },
  {
    title: '辅助功能',
    items: [
      { name: '消息中心', icon: 'message', path: '/message-center' },
      { name: '充值记录', icon: 'recharge', path: '/recharge-log' },
      { name: '客服导师', icon: 'support', path: '/support' },
    ]
  }
]);

const isActive = (path) => {
  const pathWithoutSlash = path.startsWith('/') ? path.slice(1) : path;
  return route.path === path || route.path.endsWith(pathWithoutSlash);
};

const getIconUrl = (iconName) => {
  try {
    return new URL(`/src/assets/image/dashboard/${iconName}.png`, import.meta.url).href;
  } catch (e) {
    return '';
  }
};
</script>

<template>
    <div class="h-screen w-[13.5vw] p-5 box-border overflow-y-auto flex flex-col bg-gradient-to-br from-[#001536] via-[#00001C] via-[#131722] to-[#1B2130] scrollbar-hide">
      <div class="flex items-center justify-center mb-8">
        <span class="text-[2.1vw] leading-[43px] font-bold text-white text-center">AIGC</span>
      </div>
      <nav class="flex-1">
        <div v-for="(group, groupIndex) in menuGroups" :key="groupIndex" class="mb-6">
          <h3 class="text-[0.9vw] leading-5 text-gray-400 mb-[1vw] px-[1vw]">{{ group.title }}</h3>
          <ul>
            <li v-for="(item, itemIndex) in group.items" :key="itemIndex" class="mb-2">
              <router-link
                :to="item.path"
                :class="{
                  'flex items-center px-3 py-2 rounded-md text-white transition-colors duration-200 ease-in-out cursor-pointer': true,
                  'hover:bg-gray-700': !isActive(item.path),
                  'bg-[#4361EE] font-medium': isActive(item.path)
                }"
              >
                <span class="mr-3 w-[1.2vw] h-[1.2vw] flex items-center justify-center">
                  <img
                    v-if="item.icon !== 'material'"
                    :src="getIconUrl(item.icon)"
                    loading="lazy"
                    :alt="item.name"
                    class="w-full h-full object-contain"
                  />
                </span>
                <span class="text-[1vw] leading-6">{{ item.name }}</span>
              </router-link>
            </li>
          </ul>
        </div>
      </nav>
    </div>
</template>