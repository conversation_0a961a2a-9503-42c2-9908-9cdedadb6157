import axios from 'axios';
import { ElMessage } from 'element-plus';
import Cookies from 'js-cookie';
import router from '@/router';
import { useUserStore } from '@/store/user';

const service = axios.create({
  timeout: 180000, // 增加到180秒
});

service.interceptors.request.use(
  config => {
    const token = Cookies.get('access_token');
    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

service.interceptors.response.use(
  response => {
    const res = response.data;
    const configOptions = response.config.requestOptions || {};
    const showError = configOptions.showError === true;
    const level = configOptions.level !== undefined ? configOptions.level : 3;

    if (res.code !== 0 && res.code !== 200) {
      if (showError) {
        const message = res.message || 'Error';
        const messageType = ['success', 'warning', 'info', 'error'][level] || 'error';

        ElMessage({
          message: message,
          type: messageType,
          duration: 3 * 1000,
        });
      }

      return Promise.resolve(res);
    } else {
      return res;
    }
  },
  error => {
    const configOptions = error.config?.requestOptions || {};
    const showError = configOptions.showError === true;

    if (error.response && showError) {
      const status = error.response.status;
      const data = error.response.data;
      let message = '网络错误，请稍后再试';
      if (data && data.message) {
        message = data.message;
      } else if (error.message) {
        message = error.message;
      }

      ElMessage({
        message: message,
        type: 'error',
        duration: 3 * 1000,
      });
    } else if (showError) {
        ElMessage({
            message: error.message || '网络错误，请稍后再试',
            type: 'error',
            duration: 3 * 1000,
        });
    }

    if (error.response && error.response.status === 401) {
      if (router.currentRoute.value.path !== '/') {
        ElMessage({
          message: '登录过期，请重新登录',
          type: 'error',
          duration: 3 * 1000,
        });
        useUserStore.clearUserData();
        router.push('/');
      }
    }

    return Promise.reject(error);
  }
);

/**
 * 发起 GET 请求
 * @param {string} url 请求地址
 * @param {object} params 请求参数 (查询字符串)
 * @param {object} options 配置项，包含 showError、level 和 isRefresh
 * @param {boolean} [options.showError=false] 是否显示错误提示
 * @param {number} [options.level=3] 错误提示等级 (0: success, 1: warning, 2: info, 3: error)
 * @param {boolean} [options.isRefresh=false] 请求结束后是否刷新当前路由
 * @returns {Promise}
 */
const get = (url, params = {}, options = { showError: true, level: 3}) => {
  return service({
    url: url,
    method: 'get',
    params: params,
    requestOptions: options
  });
};

/**
 * 发起 POST 请求
 * @param {string} url 请求地址
 * @param {object} data 请求体数据
 * @param {object} options 配置项，包含 showError、level、isRefresh 和 timeout
 * @param {boolean} [options.showError=false] 是否显示错误提示
 * @param {number} [options.level=3] 错误提示等级 (0: success, 1: warning, 2: info, 3: error)
 * @param {boolean} [options.isRefresh=false] 请求结束后是否刷新当前路由
 * @param {number} [options.timeout] 自定义超时时间（毫秒）
 * @returns {Promise}
 */
const post = (url, data = {}, options = { showError: true, level: 3}) => {
  const config = {
    url: url,
    method: 'post',
    data: data,
    requestOptions: options
  };

  // 如果指定了自定义超时时间，则使用它
  if (options.timeout) {
    config.timeout = options.timeout;
  }

  return service(config);
};

/**
 * 发起 DELETE 请求
 * @param {string} url 请求地址
 * @param {object} data 请求体数据
 * @param {object} options 配置项，包含 showError、level 和 isRefresh
 * @param {boolean} [options.showError=false] 是否显示错误提示
 * @param {number} [options.level=3] 错误提示等级 (0: success, 1: warning, 2: info, 3: error)
 * @param {boolean} [options.isRefresh=false] 请求结束后是否刷新当前路由
 * @returns {Promise}
 */
const del = (url, data = {}, options = { showError: true, level: 3}) => {
  return service({
    url: url,
    method: 'delete',
    data: data,
    requestOptions: options
  });
};

export { get, post, del };

export default service;