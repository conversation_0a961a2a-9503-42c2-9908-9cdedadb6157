<script setup>
import { ref, watch } from 'vue'
import MarkdownIt from 'markdown-it'

// 右侧面板组件
const activeTab = ref('分镜管理')

const tabs = ref([
  { id: 1, name: '分镜管理' },
  { id: 2, name: '图片' },
  { id: 3, name: '视频' }
])

// 分镜内容管理
const storyboardContent = ref('')
const parsedStoryboardContent = ref('')

// 初始化markdown渲染器
const md = new MarkdownIt({
  html: true,        // 允许HTML标签
  linkify: true,     // 自动识别链接
  typographer: true, // 启用一些语言中性的替换 + 引号美化
  breaks: true,      // 将换行符转换为<br>
})

// HTML转义函数
const escapeHtml = (unsafe) => {
  if (typeof unsafe !== 'string') return '';
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}

// 解析分镜内容的函数
const parseStoryboardContent = (content) => {
  const lines = content.split('\n').map(line => line.trim()).filter(line => line);
  let result = '';
  let currentStoryboard = '';
  let currentRows = [];

  for (const line of lines) {
    // 检查是否是分镜标题（如"分镜一"、"分镜二"等）
    if (line.match(/^分镜[一二三四五六七八九十\d]+$/)) {
      // 如果有之前的分镜，先处理它
      if (currentStoryboard && currentRows.length > 0) {
        result += `| ${currentStoryboard} |  |\n`;
        result += '|------|------|\n';
        for (const row of currentRows) {
          result += `| ${row.title} | ${row.content} |\n`;
        }
        result += '\n';
      }

      // 开始新的分镜
      currentStoryboard = line;
      currentRows = [];
    } else if (line.includes('：')) {
      // 解析包含冒号的行
      const colonIndex = line.indexOf('：');
      const title = line.substring(0, colonIndex);
      const content = line.substring(colonIndex + 1);
      currentRows.push({ title, content });
    }
  }

  // 处理最后一个分镜
  if (currentStoryboard && currentRows.length > 0) {
    result += `| ${currentStoryboard} |  |\n`;
    result += '|------|------|\n';
    for (const row of currentRows) {
      result += `| ${row.title} | ${row.content} |\n`;
    }
    result += '\n';
  }

  return result;
}

// 渲染分镜内容
const renderStoryboardContent = (content) => {
  if (!content) return '';

  // 检查是否是分镜格式：以[分镜]开头，然后是括号包裹的内容
  const storyboardMatch = content.match(/^\[分镜\]\s*\(\s*([\s\S]*?)\s*\)$/);

  if (storyboardMatch) {
    // 解析分镜内容
    const storyboardContent = storyboardMatch[1];
    const processedContent = parseStoryboardContent(storyboardContent);
    return md.render(processedContent);
  }

  // 如果不是分镜格式，直接使用markdown渲染
  return md.render(content);
}

// 接收分镜内容的函数
const receiveStoryboardContent = (content) => {
  console.log('RightPanel 接收到分镜内容:', content);
  storyboardContent.value = content;
  parsedStoryboardContent.value = renderStoryboardContent(content);
  console.log('解析后的分镜内容:', parsedStoryboardContent.value);
}

// 切换标签
const switchTab = (tabName) => {
  activeTab.value = tabName
}

// 监听 parsedStoryboardContent 的变化
watch(parsedStoryboardContent, (newValue) => {
  console.log('parsedStoryboardContent 发生变化:', newValue);
}, { immediate: true });

// 暴露方法给父组件
defineExpose({
  receiveStoryboardContent
})
</script>

<template>
  <div class="min-w-[20.1vw] bg-[#1B2130] border border-[#393B4A] py-[0.8333vw] overflow-hidden text-white flex flex-col">
    <!-- 标题 -->
    <div class="mb-[0.2vw] flex-shrink-0">
      <h2 class="text-[1vw] font-bold text-white font-['AppleSystemUIFont'] ml-[0.8333vw]">分镜管理</h2>
    </div>

    <!-- 标签栏 -->
    <div class="flex border-b border-[#393B4A] flex-shrink-0">
      <div
        v-for="tab in tabs"
        :key="tab.id"
        class="py-2 px-4 cursor-pointer text-center text-[0.8vw] font-['AppleSystemUIFont'] flex-1"
        :class="activeTab === tab.name ? 'text-white border-b-2 border-white' : 'text-[#AAAAAA]'"
        @click="switchTab(tab.name)"
      >
        {{ tab.name }}
      </div>
    </div>

    <!-- 标签内容区域 -->
    <div class="mt-4 flex-1 overflow-hidden px-[0.8333vw]">
      <!-- 分镜管理内容 -->
      <div v-if="activeTab === '分镜管理'" class="h-full">
        <div v-if="parsedStoryboardContent && parsedStoryboardContent.trim()" class="h-full overflow-y-auto storyboard-scroll flex flex-col items-center">
          <!-- 分镜内容显示 -->
          <div
            class="text-white text-[0.8vw] leading-relaxed markdown-content pr-[0.5vw]"
            v-html="parsedStoryboardContent"
          ></div>
        </div>
        <div v-else class="h-full flex items-center justify-center">
          <span class="text-[#8B94A3] text-[0.8vw] text-center px-4">暂无分镜内容<br/>请在对话中点击"开始任务"按钮</span>
        </div>
      </div>

      <!-- 图片内容 -->
      <div v-if="activeTab === '图片'" class="h-full flex items-center justify-center">
        <span class="text-[#8B94A3] text-[0.8vw]">图片功能开发中...</span>
      </div>

      <!-- 视频内容 -->
      <div v-if="activeTab === '视频'" class="h-full flex items-center justify-center">
        <span class="text-[#8B94A3] text-[0.8vw]">视频功能开发中...</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 分镜内容滚动条样式 - 隐藏滚动条但保持滚动功能 */
.storyboard-scroll::-webkit-scrollbar {
  display: none;
}

.storyboard-scroll {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* Markdown内容样式 */
.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  color: #FFFFFF;
  margin: 0.5em 0 0.3em 0;
  font-weight: bold;
}

.markdown-content :deep(h1) { font-size: 1.1em; }
.markdown-content :deep(h2) { font-size: 1.05em; }
.markdown-content :deep(h3) { font-size: 1em; }
.markdown-content :deep(h4) { font-size: 0.95em; }
.markdown-content :deep(h5) { font-size: 0.9em; }
.markdown-content :deep(h6) { font-size: 0.85em; }

.markdown-content :deep(p) {
  margin: 0.3em 0;
  line-height: 1.5;
}

.markdown-content :deep(table) {
  border-collapse: collapse;
  margin: 0.7em 0;
  width: 100%;
  font-size: 0.75vw;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  border: 1px solid #393B4A;
  padding: 0.3em 0.5em;
  text-align: left;
}

.markdown-content :deep(th) {
  background-color: #2A2F3A;
  font-weight: bold;
  color: #FFFFFF;
}

.markdown-content :deep(td) {
  background-color: #1F2329;
  color: #E6E6E6;
}

.markdown-content :deep(th:first-child),
.markdown-content :deep(td:first-child) {
  width: 25%;
  min-width: 70px;
}

.markdown-content :deep(code) {
  background-color: #2A2F3A;
  color: #E6E6E6;
  padding: 0.1em 0.3em;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.85em;
}

.markdown-content :deep(pre) {
  background-color: #2A2F3A;
  color: #E6E6E6;
  padding: 0.6em;
  border-radius: 6px;
  overflow-x: auto;
  margin: 0.5em 0;
  border: 1px solid #393B4A;
}

.markdown-content :deep(pre code) {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin: 0.3em 0;
  padding-left: 1.2em;
}

.markdown-content :deep(li) {
  margin: 0.2em 0;
  line-height: 1.4;
}

.markdown-content :deep(strong) {
  font-weight: bold;
  color: #FFFFFF;
}

.markdown-content :deep(em) {
  font-style: italic;
  color: #E6E6E6;
}
</style>
