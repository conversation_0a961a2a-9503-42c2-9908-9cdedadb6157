<script setup>
import { ref, onMounted, watch, reactive, nextTick } from "vue";
import { useRoute } from "vue-router";
import { get } from "@/request";
import { ElLoading, ElDialog, ElCarousel, ElCarouselItem, ElImageViewer } from "element-plus";

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return `${month}月${day}日`;
};

const route = useRoute();
const activeTab = ref("加载中...");
const courseData = ref([]);
const isLoading = ref(true);
const hasError = ref(false);
const loadingInstance = ref(null);

const activeTabIndex = ref(0);
const activeCategory = ref("AI文本");
const selectedCourseType = ref("免费课程");

const placeholderTabs = ["加载中..."];
const placeholderCategories = [];

const courseDataCache = reactive({
  free: null,
  advanced: null,
});

const contentData = ref([]);
const contentLoading = ref(false);
const contentError = ref(false);
const contentLoadingInstance = ref(null);
const activeCategoryId = ref(null);

const modalVisible = ref(false);
const selectedItem = ref(null);
const showViewer = ref(false);
const previewUrlList = ref([]);

const isInitialLoad = ref(true);

const fetchCourseData = async () => {
  if (isInitialLoad.value && selectedCourseType.value === "进阶课程") {
  }

  isLoading.value = true;
  hasError.value = false;

  const title = selectedCourseType.value === "免费课程" ? "free" : "advanced";

  if (courseDataCache[title]) {
    courseData.value = courseDataCache[title];

    if (courseData.value.length > 0) {
      activeTab.value = courseData.value[0].level_one;

      const firstTabChildren = courseData.value[0].children;
      if (firstTabChildren && firstTabChildren.length > 0) {
        activeCategory.value = firstTabChildren[0].level_two;
        activeCategoryId.value = firstTabChildren[0].id;
        fetchContentData(firstTabChildren[0].id);
      } else {
        activeCategory.value = "";
        activeCategoryId.value = null;
        contentData.value = [];
      }
    } else {
      activeTab.value = "";
      activeCategory.value = "";
      activeCategoryId.value = null;
      contentData.value = [];
    }

    isLoading.value = false;
    return;
  }

  loadingInstance.value = ElLoading.service({
    target: ".course-container",
    text: "正在获取...",
    background: "rgba(0, 0, 0, 0.7)",
  });

  try {
    const response = await get(`/api/course/list/?title=${title}`);

    if (response.code === 200 && response.data && response.data.length > 0) {
      courseData.value = response.data;
      courseDataCache[title] = response.data;

      if (courseData.value.length > 0) {
        activeTab.value = courseData.value[0].level_one;

        const firstTabChildren = courseData.value[0].children;
        if (firstTabChildren && firstTabChildren.length > 0) {
          activeCategory.value = firstTabChildren[0].level_two;
          activeCategoryId.value = firstTabChildren[0].id;
          fetchContentData(firstTabChildren[0].id);
        } else {
          activeCategory.value = "";
          activeCategoryId.value = null;
          contentData.value = [];
        }
      } else {
        activeTab.value = "";
        activeCategory.value = "";
        activeCategoryId.value = null;
        contentData.value = [];
      }
    } else {
      courseData.value = [];
      activeTab.value = "";
      activeCategory.value = "";
      activeCategoryId.value = null;
      contentData.value = [];
    }
  } catch (error) {
    hasError.value = true;
    courseData.value = [];
    contentData.value = [];
  } finally {
    if (loadingInstance.value) {
      loadingInstance.value.close();
    }
    isLoading.value = false;
  }
};

const fetchContentData = async (categoryId) => {
  if (!categoryId) return;

  contentData.value = [];
  contentLoading.value = true;
  contentError.value = false;

  try {
    const response = await get(`/api/course/contents/?id=${categoryId}`);

    if (response.code === 200 && response.data && response.data.list) {
      contentData.value = response.data.list;
    } else {
      contentData.value = [];
    }
  } catch (error) {
    contentError.value = true;
    contentData.value = [];
  } finally {
    if (contentLoadingInstance.value) {
      contentLoadingInstance.value.close();
    }
    contentLoading.value = false;
  }
};

onMounted(async () => {
  const typeFromQuery = route.query.type;

  if (typeFromQuery === "free") {
    selectedCourseType.value = "免费课程";
  } else if (typeFromQuery === "advanced") {
    selectedCourseType.value = "进阶课程";
  }
  await fetchCourseData();
  await nextTick();
  isInitialLoad.value = false;
});

watch(
  () => selectedCourseType.value,
  (newValue, oldValue) => {
    if (isInitialLoad.value) {
      return;
    }

    fetchCourseData();
  }
);

const setActiveTab = (tab, index) => {
  activeTab.value = tab;
  activeTabIndex.value = index;

  contentData.value = [];

  const currentTabChildren = courseData.value[index].children;
  if (currentTabChildren && currentTabChildren.length > 0) {
    activeCategory.value = currentTabChildren[0].level_two;
    activeCategoryId.value = currentTabChildren[0].id;
    fetchContentData(currentTabChildren[0].id);
  } else {
    activeCategory.value = "";
    activeCategoryId.value = null;
  }
};

const setActiveCategory = (category, id) => {
  activeCategory.value = category;
  activeCategoryId.value = id;
  contentData.value = [];
  fetchContentData(id);
};

const setCourseType = (type) => {
  selectedCourseType.value = type;
};

const handleImageLoad = (event) => {
};

const carouselRef = ref(null);

const openItemModal = (item) => {
  selectedItem.value = item;
  modalVisible.value = true;
  nextTick(() => {
    if (carouselRef.value) {
      carouselRef.value.setActiveItem(0);
    }
  });
};

const handleImageClick = (url) => {
  previewUrlList.value = [url];
  showViewer.value = true;
};

const closeViewer = () => {
  showViewer.value = false;
  previewUrlList.value = [];
};

const closeModal = () => {
  modalVisible.value = false;
};
</script>

<template>
  <div class="flex flex-col h-full course-container relative">
    <div class="w-full mb-[1vw]">
      <div class="flex w-full">
        <div
          @click="setCourseType('免费课程')"
          :class="{
            'w-1/2 text-center py-[0.75vw] text-[1.4vw] leading-[1.363vw] font-bold cursor-pointer transition-colors': true,
            'text-[#C9DBFF] bg-gradient-to-t from-[#243670] to-[#0C153D] shadow-[0_4px_6px_rgba(36,54,112,0.8)]':
              selectedCourseType === '免费课程',
            'text-[#AAAAAA] bg-gradient-to-t from-[#324161] to-[#0A172B] hover:text-[#C9DBFF]':
              selectedCourseType !== '免费课程',
          }"
        >
          免费课程
        </div>
        <div
          @click="setCourseType('进阶课程')"
          :class="{
            'w-1/2 text-center py-[0.75vw] text-[1.4vw] leading-[1.363vw] font-bold cursor-pointer transition-colors hover:text-white': true,
            'text-[#C9DBFF] bg-gradient-to-t from-[#243670] to-[#0C153D] shadow-[0_4px_6px_rgba(36,54,112,0.8)]':
              selectedCourseType === '进阶课程',
            'text-[#AAAAAA] bg-gradient-to-t from-[#324161] to-[#0A172B] hover:text-[#C9DBFF]':
              selectedCourseType !== '进阶课程',
          }"
        >
          进阶课程
        </div>
      </div>
    </div>

    <div class="flex-1">
      <div
        class="border border-[#5D85C9] mb-[0.8vw] bg-[#1B2130] py-[0.1vw] px-[0.833vw]"
      >
        <div class="flex">
          <button
            v-for="(tab, index) in isLoading ? placeholderTabs : courseData"
            :key="isLoading ? index : tab.id"
            @click="!isLoading && setActiveTab(tab.level_one, index)"
            class="relative cursor-pointer group"
          >
            <p
              class="py-[0.90vw] px-[0.833vw] font-medium text-white text-[1.1vw] leading-[1vw]"
            >
              {{ isLoading ? tab : tab.level_one }}
            </p>
            <div
              v-if="
                (!isLoading && activeTab === tab.level_one) ||
                (isLoading && activeTab === tab)
              "
              class="absolute bottom-[0.3vw] left-[25%] right-[25%] h-[0.25vw] bg-[#00F7FF]"
            ></div>
            <div
              class="absolute bottom-[0.3vw] left-[25%] right-[25%] h-[0.25vw] bg-[#00F7FF] opacity-0 transition-opacity duration-300 group-hover:opacity-30"
            ></div>
          </button>

          <div
            v-if="!isLoading && courseData.length === 0"
            class="w-full py-[0.90vw] px-[0.833vw] text-center text-white"
          >
            没有菜单数据
          </div>
        </div>
      </div>

      <div class="flex space-x-[0.633vw] mb-[0.8vw]">
        <template v-if="isLoading">
          <button
            v-for="(category, index) in placeholderCategories"
            :key="index"
            class="py-[0.5vw] px-[1.250vw] rounded-[0.417vw] text-white font-medium transition-colors border border-[#6B86FA] cursor-pointer"
            :class="{
              'bg-[#1C3175]': activeCategory === category,
              'bg-[#29354C] hover:bg-[#1C3175] border border-[#616F8C]':
                activeCategory !== category,
            }"
          >
            {{ category }}
          </button>
        </template>

        <div
          v-else-if="
            courseData.length === 0 ||
            !courseData[activeTabIndex] ||
            !courseData[activeTabIndex].children ||
            courseData[activeTabIndex].children.length === 0
          "
          class="w-full py-[0.5vw] text-center text-white"
        ></div>

        <template v-else>
          <button
            v-for="category in courseData[activeTabIndex].children"
            :key="category.id"
            @click="setActiveCategory(category.level_two, category.id)"
            :class="{
              'py-[0.5vw] px-[1.250vw] rounded-[0.417vw] text-white font-medium transition-colors border border-[#6B86FA] cursor-pointer': true,
              'bg-[#1C3175]': activeCategory === category.level_two,
              'bg-[#29354C] hover:bg-[#1C3175] border border-[#616F8C]':
                activeCategory !== category.level_two,
            }"
          >
            {{ category.level_two }}
          </button>
        </template>
      </div>

      <div class="content-container relative">
        <div v-if="contentLoading" class="text-center py-8 text-white">
          加载中...
        </div>

        <div v-else-if="contentError" class="text-center py-8 text-white">
          加载失败，请重试
        </div>

        <div v-else-if="contentData.length === 0" class="text-center py-8 text-white">
          暂无内容
        </div>

        <div v-else class="masonry-container">
          <div
            v-for="item in contentData"
            :key="item.id"
            class="masonry-item course-card bg-[#1B2130] border border-[#444444] rounded-[4px] overflow-hidden transition-all hover:scale-[1.02] cursor-pointer"
            @click="openItemModal(item)"
          >
            <div class="relative">
              <img
                :src="item.cover"
                :alt="item.title"
                class="w-full object-cover"
                loading="lazy"
                @load="handleImageLoad"
              />
            </div>

            <div class="p-[0.625vw]">
              <div class="text-white text-[1.1vw] font-medium truncate">{{ item.title }}</div>
              <div class="flex justify-between items-center my-[0.5vw]">
                <span class="text-[#8B94A3] text-[0.9vw] ml-[0.3vw]">浏览量：{{ item.view }}</span>
                <button class="bg-[#223380] text-white px-[1vw] py-[0.5vw] rounded-[6px] text-[0.9vw] leading-[1vw] shadow-[0_10px_12px_0_#3551CD] border-r-[1px] border-[#3551CD]">立即订阅</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <el-dialog
    v-model="modalVisible"
    :title="null"
    top="8vh"
    width="60%"
    :show-close="false"
    @close="closeModal"
    class="policy-modal-bg-dialog"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
  >
    <div v-if="selectedItem" class="modal-content flex rounded-[15px] overflow-hidden">
      <div class="w-1/2 bg-black px-[3px]">
        <el-carousel ref="carouselRef" v-if="selectedItem.medias && selectedItem.medias.length > 0" height="80vh" indicator-position="outside" arrow="always">
          <el-carousel-item v-for="(media, index) in selectedItem.medias" :key="index">
            <template v-if="media.path.endsWith('.mp4') || media.path.endsWith('.webm') || media.path.endsWith('.ogg')">
              <video :src="media.path" controls class="w-full h-full object-contain"></video>
            </template>
            <template v-else>
              <img :src="media.path" :alt="media.filename" loading="lazy" class="w-full h-full object-contain" @click="handleImageClick(media.path)" />
            </template>
          </el-carousel-item>
        </el-carousel>
        <div v-else class="flex items-center justify-center h-full">
          <img :src="selectedItem.cover" :alt="selectedItem.title" loading="lazy" class="max-w-full max-h-[400px] object-contain" @click="handleImageClick(selectedItem.cover)" />
        </div>
      </div>

      <div class="modal-right w-1/2 p-6 bg-[#1B2130] text-white flex flex-col relative">
        <h2 class="text-2xl font-bold mb-4">{{ selectedItem.title }}</h2>
        <div class="content-area rich-text-display mb-6 flex-1 overflow-hidden scrollbar-hide" v-html="selectedItem.content"></div>
        <div class="text-[#8B94A3] text-sm absolute bottom-6 left-6">
          发布时间: {{ formatDate(selectedItem.created_at) }}
        </div>
      </div>
    </div>
  </el-dialog>

  <el-image-viewer
    v-if="showViewer"
    :url-list="previewUrlList"
    @close="closeViewer"
  />
</template>

<style lang="scss" scoped>
.course-container {
  position: relative;
}

.content-container {
  min-height: 200px;
}

.course-card {
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  &:hover {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.2);
    border-color: gray;
  }
}

.masonry-container {
  column-count: 4;
  column-gap: 1rem;
  width: 100%;

  @media (max-width: 1200px) {
    column-count: 3;
  }

  @media (max-width: 768px) {
    column-count: 2;
  }

  @media (max-width: 480px) {
    column-count: 1;
  }
}

.masonry-item {
  break-inside: avoid;
  margin-bottom: 1rem;
  display: inline-block;
  width: 100%;
}

:deep(.el-loading-mask) {
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 10;
}

:deep(.el-loading-spinner) {
  .circular {
    width: 2vw;
    height: 2vw;
  }

  .el-loading-text {
    color: #fff;
    font-size: 1vw;
    margin-top: 0.5vw;
  }

  .path {
    stroke: #00f7ff;
  }
}

/* Modal styles */
:deep(.course-detail-modal) {
  .el-dialog {
    border-radius: 12px;
    overflow: hidden;
    background-color: transparent;
    padding: 0 !important;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
  }

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }

  .modal-content {
    border-radius: 12px;
    overflow: hidden;
  }
}

:deep(.el-carousel) {
  .el-carousel__indicators {
    bottom: -20px;
  }

  .el-carousel__arrow {
    background-color: rgba(0, 0, 0, 0.6);

    &:hover {
      background-color: rgba(0, 0, 0, 0.8);
    }
  }
}

:deep(.course-modal-header) {
  display: none !important;
  padding: 0 !important;
}

.content-area {
  max-height: 65vh;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #2c3e50;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #4a6b8a;
    border-radius: 3px;
  }
}

.rich-text-display {
  :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
    margin-top: 1em;
    margin-bottom: 0.5em;
    font-weight: bold;
    line-height: 1.2;
  }

  :deep(h1) { font-size: 2em; }
  :deep(h2) { font-size: 1.5em; }
  :deep(h3) { font-size: 1.17em; }
  :deep(h4) { font-size: 1em; }
  :deep(h5) { font-size: 0.83em; }
  :deep(h6) { font-size: 0.67em; }

  :deep(p) {
    margin-bottom: 1em;
    line-height: 1.6;
  }

  :deep(ul), :deep(ol) {
    margin-left: 1.5em;
    margin-bottom: 1em;
    list-style-type: disc; /* Default for ul */
  }

  :deep(ol) {
    list-style-type: decimal;
  }

  :deep(li) {
    margin-bottom: 0.5em;
    line-height: 1.6;
  }

  :deep(strong) {
    font-weight: bold;
  }

  :deep(span) {
    line-height: inherit;
  }
}
</style>