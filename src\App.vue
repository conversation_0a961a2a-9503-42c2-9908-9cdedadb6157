<script setup>
import { useRouter } from 'vue-router'
import { RouterView } from 'vue-router'
const router = useRouter()
</script>

<template>
  <Transition
    enter-active-class="animate__animated animate__fadeIn"
    leave-active-class="animate__animated animate__fadeOut"
  >
    <RouterView />
  </Transition>
</template>

<style lang="scss">
.el-input__wrapper {
  background-color: #303A54 !important;
  border: 1px solid #8B94A3 !important;
  box-shadow: none !important;
  border-radius: 0 !important;

  .el-input__inner {
    color: white !important;
  }
}

.custom-add-image {
  background-color: #1B2130 !important;
  border: 1px solid #474D59 !important;
  cursor: pointer !important;
}

.el-popper.is-customadd {
  background-color: #1B2130 !important;
  border: 1px solid #474D59 !important;
}

.el-popper.is-customadd .el-popper__arrow::before {
  background-color: #1B2130 !important;
  border-bottom: 1px solid #474D59 !important;
  border-left: 1px solid #474D59 !important;
}

.el-popper.is-customadd .el-popper__arrow::after {
  border-right-color: #474D59 !important;
}

.el-popper.is-customadd-top {
  background-color: #1B2130 !important;
  border: 1px solid #474D59 !important;
}

.el-popper.is-customadd-top .el-popper__arrow::before {
  background-color: #1B2130 !important;
  border: 1px solid #474D59 !important;
  border-left-color: transparent !important;
  border-top-color: transparent !important;
}

.el-message-box {
  background-color: #1B2130 !important;
  border: 1px solid #474D59 !important;

  .el-message-box__title {
    color: white !important;
  }

  .el-message-box__content {
    color: white !important;
  }

  .el-message-box__btns {
    button {
      background-color: #303A54 !important;
      border: 1px solid #8B94A3 !important;
      color: white !important;
    }
  }
}

.custom-tooltip {
  background-color: #2B428C !important;
  color: white !important;
  border: 1px solid #8B94A3 !important;
  height: 1.3vw;
  margin-bottom: -0.65vw !important;
  display: flex;
  border-radius: 0 !important;
  justify-content: center;
  align-items: center;
  z-index: 0 !important;
}

.custom-tooltip-dialog {
  background-color: #2B428C !important;
  color: white !important;
  border: 1px solid #8B94A3 !important;
  height: 1.3vw;
  margin-bottom: -0.25vw !important;
  display: flex;
  border-radius: 0 !important;
  justify-content: center;
  align-items: center;
}

.el-popper.custom-tooltip-dialog .el-popper__arrow::before {
  background-color: #2B428C !important;
  border-bottom-color: #8B94A3 !important;
  border-right-color: #8B94A3 !important;
}

.el-popper.custom-tooltip-dialog .el-popper__arrow::after {
  border-top-color: #8B94A3 !important;
}

.el-popper.custom-tooltip .el-popper__arrow::before {
  background-color: #2B428C !important;
  border-bottom-color: #8B94A3 !important;
  border-right-color: #8B94A3 !important;
}

.el-popper.custom-tooltip .el-popper__arrow::after {
  border-top-color: #8B94A3 !important;
}

.el-popper.is-customized {
  padding: 0.2vw 0.7vw;
  border-radius: 0;
  font-size: 0.8vw;
  border: 1px solid #8B94A3;
  background-color: #2B428C;
}

.el-popper.is-customized .el-popper__arrow::before {
  background-color: #2B428C;
  border: 1px solid #8B94A3;
  border-right-color: #8B94A3;
  right: 0;
}

.el-popper.is-customized .el-popper__arrow::after {
  border-top-color: #8B94A3;
}

.policy-modal-bg-dialog {
  padding: 0 !important;
  border-radius: 15px !important;
  border: 1px solid #8B94A3 !important;
}

.policy-modal-bg-dialog .el-dialog__header {
  display: none !important;
}

.el-slider__button {
  height: 10px !important;
  width: 10px !important;
}

.el-switch__label.is-active {
  color: #FFFFFF !important;
}
</style>
