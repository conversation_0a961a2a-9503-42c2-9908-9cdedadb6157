<script setup>
import { ref, onMounted, watch } from 'vue'
import { get } from '@/request.js'
import { useUserStore } from '@/store/user'
import LeftPanel from './LeftPanel.vue'
import MiddlePanel from './MiddlePanel.vue'
import RightPanel from './RightPanel.vue'

const userStore = useUserStore()

// 共享状态
const messages = ref([])
const historyItems = ref([])
const isLoading = ref(false)
const hasMoreItems = ref(true)
const currentPage = ref(1)
const ITEMS_PER_PAGE = 10
const activeTab = ref('文生视频')

const tabs = ref([
  { id: 'textToVideo', name: '文生视频' },
  { id: 'imageToVideo', name: '图生视频' },
  { id: 'referenceToVideo', name: '参考生视频' }
])

const apiEndpoints = {
  '文生视频': {
    generate: '/api/text2video/generate',
    status: '/api/text2video/status',
    list: '/api/text2video/list'
  },
  '图生视频': {
    generate: '/api/image2video/generate',
    status: '/api/image2video/status',
    list: '/api/image2video/list'
  },
  '参考生视频': {
    generate: '/api/reference2video/generate',
    status: '/api/reference2video/status',
    list: '/api/reference2video/list'
  }
}

// 历史记录获取函数
const fetchHistoryItems = async (isLoadMore = false) => {
  if (isLoading.value) return;
  if (isLoadMore && !hasMoreItems.value) return;

  isLoading.value = true;

  let processingHistoryItems = [];
  if (!isLoadMore) {
    currentPage.value = 1;
    processingHistoryItems = historyItems.value.filter(item =>
      ['created', 'queueing', 'processing', 'initiating'].includes(item.state) && item.id.startsWith('temp-history-')
    );
    historyItems.value = [];
    messages.value = messages.value.filter(m => !m.isFromHistory);
    hasMoreItems.value = true;
  }

  try {
    const response = await get(`${apiEndpoints[activeTab.value].list}?page=${currentPage.value}`);
    if (response.code === 200 && response.data && response.data.items) {
      const backendItems = response.data.items;
      if (backendItems.length > 0) {
        let combinedItems;
        if (isLoadMore) {
          const existingTaskIds = new Set(historyItems.value.map(h => h.task_id));
          const uniqueBackendItems = backendItems.filter(b => !existingTaskIds.has(b.task_id));
          historyItems.value.push(...uniqueBackendItems);
        } else {
          const backendTaskIds = new Set(backendItems.map(b => b.task_id));
          const uniqueProcessingItems = processingHistoryItems.filter(p => !backendTaskIds.has(p.task_id));

          combinedItems = [...uniqueProcessingItems, ...backendItems];
          historyItems.value = combinedItems;
        }

        const itemsToProcessForMessages = isLoadMore ? backendItems : historyItems.value;

        itemsToProcessForMessages.forEach(item => {
          const historyMessage = {
            id: item.id,
            type: 'ai',
            prompt: item.prompt,
            timestamp: new Date(item.created_at).toLocaleString(),
            status: item.state,
            taskId: item.task_id,
            imageUrl: item.creations?.[0]?.cover_url || '/assets/image/public/loading.gif',
            videoUrl: item.creations?.[0]?.url || '',
            isFromHistory: true,
            messageId: `message-${item.task_id}`
          };
          if (!messages.value.some(msg => msg.taskId === item.task_id && msg.isFromHistory)) {
             messages.value.push(historyMessage);
          }
        });

        if (backendItems.length < ITEMS_PER_PAGE) {
          hasMoreItems.value = false;
        } else {
          currentPage.value++;
        }
      } else {
        hasMoreItems.value = false;
      }
    } else {
      if (!isLoadMore) hasMoreItems.value = false;
    }
  } catch (error) {
    if (!isLoadMore) hasMoreItems.value = false;
  } finally {
    isLoading.value = false;
  }
};

// 显示历史记录项
const showHistoryItem = (item) => {
  const historyMessage = {
    type: 'ai',
    prompt: item.prompt,
    timestamp: new Date(item.created_at).toLocaleString(),
    status: item.state === 'success' ? 'success' : item.state,
    taskId: item.task_id,
    imageUrl: item.creations?.[0]?.cover_url || '/assets/image/public/loading.gif',
    videoUrl: item.creations?.[0]?.url || '',
    isFromHistory: true,
    messageId: `message-${item.task_id}`
  };

  const existingMessageIndex = messages.value.findIndex(msg => msg.taskId === item.task_id);
  if (existingMessageIndex === -1) {
    messages.value.push(historyMessage);
  }

  // 滚动到对应的消息
  setTimeout(() => {
    const messageElement = document.getElementById(`message-${item.task_id}`);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, 100);
};

// 监听标签页变化
watch(activeTab, () => {
  fetchHistoryItems(false);
});

onMounted(() => {
  fetchHistoryItems(false);
  userStore.fetchConfigPrice();
})
</script>

<template>
  <div class="flex h-[98%] gap-x-3 text-white">
    <LeftPanel
      :tabs="tabs"
      :active-tab="activeTab"
      :api-endpoints="apiEndpoints"
      @update:active-tab="activeTab = $event"
      @refresh-history="fetchHistoryItems"
    />
    <MiddlePanel
      :messages="messages"
      :is-loading="isLoading"
      :has-more-items="hasMoreItems"
      :current-page="currentPage"
      :api-endpoints="apiEndpoints"
      :active-tab="activeTab"
      @refresh-history="fetchHistoryItems"
    />
    <RightPanel
      :history-items="historyItems"
      :is-loading="isLoading"
      :has-more-items="hasMoreItems"
      :current-page="currentPage"
      @load-more="fetchHistoryItems"
      @show-history-item="showHistoryItem"
    />
  </div>
</template>
