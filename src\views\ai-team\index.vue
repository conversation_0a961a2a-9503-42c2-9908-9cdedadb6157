<script setup>
import { ref, onMounted, computed, nextTick } from 'vue';
import { get } from "@/request";
import { useUserStore } from '@/store/user';
import LeftPanel from './LeftPanel.vue';
import MiddlePanel from './MiddlePanel.vue';
import RightPanel from './RightPanel.vue';

const userStore = useUserStore();

// 模型数据
const models = ref([])
// 组件引用
const leftPanelRef = ref(null)
const middlePanelRef = ref(null)
const rightPanelRef = ref(null)

// 右侧面板显示控制
const showRightPanel = ref(false)
// 组件是否已初始化
const isInitialized = ref(false)

// 计算中间面板的样式
const middlePanelStyle = computed(() => ({
  width: showRightPanel.value ? '45vw' : '66vw',
  transition: isInitialized.value ? 'width 0.3s ease-in-out' : 'none'
}))

const GetConfig = async () => {
  const res = await get("/api/model/list")
  console.log(res)
  if (res && res.data && res.data.models) {
    models.value = res.data.models
  }
}

// 处理新建任务事件
const handleNewTask = () => {
  // 隐藏右侧面板
  showRightPanel.value = false;

  if (middlePanelRef.value) {
    middlePanelRef.value.clearChat();
  }
}

// 处理加载聊天记录事件
const handleLoadChat = (chatItem) => {
  if (middlePanelRef.value) {
    middlePanelRef.value.loadChatHistory(chatItem);
  }
}

// 处理聊天记录被删除事件
const handleChatDeleted = (chatId) => {
  // 隐藏右侧面板
  showRightPanel.value = false;

  if (middlePanelRef.value) {
    middlePanelRef.value.clearChat();
  }
}

// 处理新聊天创建事件
const handleNewChatCreated = (chatInfo) => {
  // 通知LeftPanel刷新历史记录
  if (leftPanelRef.value) {
    leftPanelRef.value.refreshChatHistorySeamlessly();
  }
}

// 处理开始任务事件
const handleStartTask = (storyboardContent) => {
  console.log('handleStartTask 被调用，内容:', storyboardContent);

  // 显示右侧面板
  showRightPanel.value = true;
  console.log('右侧面板显示状态:', showRightPanel.value);

  // 递归重试传递内容的函数
  const tryPassContent = (retryCount = 0) => {
    console.log(`尝试传递内容，第 ${retryCount + 1} 次，ref 状态:`, rightPanelRef.value);

    if (rightPanelRef.value) {
      rightPanelRef.value.receiveStoryboardContent(storyboardContent);
      console.log('成功调用 receiveStoryboardContent');
    } else if (retryCount < 10) {
      // 最多重试10次，每次间隔100ms
      setTimeout(() => {
        tryPassContent(retryCount + 1);
      }, 100);
    } else {
      console.error('重试10次后仍无法获取 rightPanelRef');
    }
  };

  // 等待 RightPanel 组件挂载后再传递内容
  nextTick(() => {
    tryPassContent();
  });
}

onMounted(async () => {
  // 确保初始状态正确
  showRightPanel.value = false;

  await GetConfig();
  userStore.fetchConfigPrice();

  // 延迟启用过渡效果，确保初始渲染完成
  setTimeout(() => {
    isInitialized.value = true;
  }, 100);
})
</script>

<template>
  <div class="flex h-[98%] gap-x-3 text-white">
    <LeftPanel
      ref="leftPanelRef"
      @new-task="handleNewTask"
      @load-chat="handleLoadChat"
      @chat-deleted="handleChatDeleted"
    />
    <MiddlePanel
      ref="middlePanelRef"
      :models="models"
      :style="middlePanelStyle"
      @refresh-models="GetConfig"
      @new-chat-created="handleNewChatCreated"
      @start-task="handleStartTask"
    />
    <Transition name="slide-right">
      <RightPanel
        v-if="showRightPanel"
        ref="rightPanelRef"
      />
    </Transition>
  </div>
</template>

<style scoped>
/* 右侧面板滑入动画 */
.slide-right-enter-active,
.slide-right-leave-active {
  transition: all 0.3s ease-in-out;
}

.slide-right-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.slide-right-leave-to {
  opacity: 0;
  transform: translateX(100%);
}
</style>

