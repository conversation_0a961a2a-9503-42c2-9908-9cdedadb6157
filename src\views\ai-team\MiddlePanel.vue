<script setup>
import { ref, watch, onMounted, computed, onUnmounted, nextTick } from "vue";
import {
  ElSwitch,
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElSlider,
  ElMessage,
  ElTooltip,
  ElMessageBox,
  ElImageViewer,
} from "element-plus";
import { get, post, del } from "@/request";
import { useUserStore } from "@/store/user";
import Cookies from 'js-cookie';
import MarkdownIt from 'markdown-it';

// 导入展开收缩图标
import expandIcon from '@/assets/image/ai-team/expand.png';
import contractionIcon from '@/assets/image/ai-team/contraction.png';

const userStore = useUserStore();
const isTaskText = ref("开始任务");
const startTask = ref(false);
// 初始化markdown渲染器
const md = new MarkdownIt({
  html: true,        // 允许HTML标签
  linkify: true,     // 自动识别链接
  typographer: true, // 启用一些语言中性的替换 + 引号美化
  breaks: true,      // 将换行符转换为<br>
});

// 渲染消息内容的函数
const renderMessageContent = (message) => {
  if (message.type === 'ai') {
    const content = message.content || '';

    // 检查是否是分镜格式：以[分镜]开头，然后是括号包裹的内容
    const storyboardMatch = content.match(/^\[分镜\]\s*\(\s*([\s\S]*?)\s*\)$/);

    if (storyboardMatch) {
      // 解析分镜内容
      const storyboardContent = storyboardMatch[1];
      const processedContent = parseStoryboardContent(storyboardContent);
      return md.render(processedContent);
    }

    // AI消息使用markdown渲染
    return md.render(content);
  } else {
    // 用户消息保持原始文本，但需要转义HTML并保留换行
    return escapeHtml(message.content || '').replace(/\n/g, '<br>');
  }
};

// 解析分镜内容的函数
const parseStoryboardContent = (content) => {
  const lines = content.split('\n').map(line => line.trim()).filter(line => line);
  let result = '';
  let currentStoryboard = '';
  let currentRows = [];

  for (const line of lines) {
    // 检查是否是分镜标题（如"分镜一"、"分镜二"等）
    if (line.match(/^分镜[一二三四五六七八九十\d]+$/)) {
      // 如果有之前的分镜，先处理它
      if (currentStoryboard && currentRows.length > 0) {
        result += `| ${currentStoryboard} |  |\n`;
        result += '|------|------|\n';
        for (const row of currentRows) {
          result += `| ${row.title} | ${row.content} |\n`;
        }
        result += '\n';
      }

      // 开始新的分镜
      currentStoryboard = line;
      currentRows = [];
    } else if (line.includes('：')) {
      // 解析包含冒号的行
      const colonIndex = line.indexOf('：');
      const title = line.substring(0, colonIndex);
      const content = line.substring(colonIndex + 1);
      currentRows.push({ title, content });
    }
  }

  // 处理最后一个分镜
  if (currentStoryboard && currentRows.length > 0) {
    result += `| ${currentStoryboard} |  |\n`;
    result += '|------|------|\n';
    for (const row of currentRows) {
      result += `| ${row.title} | ${row.content} |\n`;
    }
    result += '\n';
  }

  return result;
};

// HTML转义函数
const escapeHtml = (unsafe) => {
  if (typeof unsafe !== 'string') return '';
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
};

// Props
const props = defineProps({
  models: {
    type: Array,
    default: () => [],
  },
});

// Emits
const emit = defineEmits(["refresh-models", "new-chat-created", "start-task"]);

const storyboardMode = ref(false);
const digitalPersonMode = ref(false);
const teamCollaboration = ref(false);

// 分镜模式工作模式选择
const storyboardWorkMode = ref(''); // 'auto' 自动工作, 'supervised' 监督工作

const userInput = ref("");
const isGenerating = ref(false);

// 聊天消息相关
const chatMessages = ref([]);
const chatContainer = ref(null);

// 聊天会话管理
const currentChatId = ref(null);
const currentChatTitle = ref('');
const currentChatType = ref('');
const isFirstMessage = ref(true);
const isLoadingChatHistory = ref(false);

// 图片上传相关
const uploadedImages = ref([]);
const imageInputRef = ref(null);

// 图片预览相关
const showImageViewer = ref(false);
const currentImageIndex = ref(0);
const previewImageList = computed(() =>
  uploadedImages.value.map(img => img.preview)
);

const createMemberDialogVisible = ref(false);
const createMemberForm = ref({
  name: "",
  avatar: null,
  avatarFile: null, // 保存原始文件对象用于上传
  prompt: "",
  temperature: 0.7,
});
const avatarInputRef = ref(null);
const isUploadingAvatar = ref(false);

// 录音相关状态
const recordDialogVisible = ref(false);
const isRecording = ref(false);
const recordingTime = ref(0);
const recordingTimer = ref(null);
const mediaRecorder = ref(null);
const audioChunks = ref([]);
const audioContext = ref(null);
const analyser = ref(null);
const microphone = ref(null);
const waveformData = ref(new Array(50).fill(0));
const animationId = ref(null);
const recordedAudioBlob = ref(null);
const isTranscribing = ref(false);

// AI优化模态框相关状态
const aiOptimizeDialogVisible = ref(false);
const aiOptimizeInput = ref('');
const currentOptimizeMessageId = ref(null);

const checkedFixedModels = ref([]);
const checkedCustomModels = ref([]);
const selectedModelId = ref(null);

// 计算当前模式的积分
const currentModelPoints = computed(() => {
  // 根据不同模式返回对应的积分
  if (storyboardMode.value) {
    return userStore.getStoryboardPrice;
  } else if (digitalPersonMode.value) {
    return userStore.getDigitalPrice;
  } else if (teamCollaboration.value) {
    return userStore.getTeamworkPrice;
  } else {
    // 普通模式下，获取当前选中的员工积分
    let selectedModel = null;

    if (checkedFixedModels.value.length > 0) {
      selectedModel = fixedModels.value.find(model => checkedFixedModels.value.includes(model.id));
    } else if (checkedCustomModels.value.length > 0) {
      selectedModel = customModels.value.find(model => checkedCustomModels.value.includes(model.id));
    }

    // 如果找到选中的员工且有points属性，返回其积分值，否则返回默认值
    if (selectedModel && selectedModel.points !== undefined) {
      return selectedModel.points;
    }

    // 如果没有找到或没有points属性，返回store中的默认值
    return userStore.getTeamPrice;
  }
});

const fixedModels = computed(() =>
  props.models.filter((model) => model.member_id === "0")
);
const customModels = computed(() =>
  props.models.filter((model) => model.member_id !== "0")
);

const scrollContainer = ref(null);
const scrollContainer2 = ref(null);

watch(storyboardMode, (newVal) => {
  if (newVal) {
    digitalPersonMode.value = false;
    teamCollaboration.value = false;
    // 开启分镜模式时，清空所有员工勾选
    checkedFixedModels.value = [];
    checkedCustomModels.value = [];
  } else {
    // 关闭分镜模式时，清空工作模式选择，恢复默认员工勾选
    storyboardWorkMode.value = '';
    if (fixedModels.value.length > 0) {
      checkedFixedModels.value = [fixedModels.value[0].id];
    }
  }
});

watch(digitalPersonMode, (newVal) => {
  if (newVal) {
    storyboardMode.value = false;
    teamCollaboration.value = false;
    // 开启数字人模式时，清空所有员工勾选
    checkedFixedModels.value = [];
    checkedCustomModels.value = [];
  } else {
    // 关闭数字人模式时，恢复默认员工勾选
    if (fixedModels.value.length > 0) {
      checkedFixedModels.value = [fixedModels.value[0].id];
    }
  }
});

// 监听团队协作开关
watch(teamCollaboration, (newVal) => {
  if (newVal) {
    // 打开团队协作：关闭其他模式，固定员工全部勾选，自建员工不勾选
    storyboardMode.value = false;
    digitalPersonMode.value = false;
    checkedFixedModels.value = fixedModels.value.map(model => model.id);
    checkedCustomModels.value = [];
  } else {
    // 关闭团队协作：初始化回到第一个固定员工的勾选
    if (fixedModels.value.length > 0) {
      checkedFixedModels.value = [fixedModels.value[0].id];
    } else {
      checkedFixedModels.value = [];
    }
    checkedCustomModels.value = [];
  }
});

onMounted(async () => {
  if (scrollContainer.value) {
    scrollContainer.value.addEventListener("wheel", (e) => {
      e.preventDefault();
      const scrollAmount = e.deltaY * 2; // 增加滚动速度
      scrollContainer.value.scrollLeft += scrollAmount;
    });
  }

  if (scrollContainer2.value) {
    scrollContainer2.value.addEventListener("wheel", (e) => {
      e.preventDefault();
      const scrollAmount = e.deltaY * 2; // 增加滚动速度
      scrollContainer2.value.scrollLeft += scrollAmount;
    });
  }
});

// 监听 fixedModels，确保在数据加载后默认勾选第一个
const unwatchFixedModels = watch(fixedModels, (newVal) => {
  if (newVal.length > 0 && checkedFixedModels.value.length === 0) {
    checkedFixedModels.value = [newVal[0].id];
    unwatchFixedModels(); // 勾选后停止监听，只执行一次
  }
}, { immediate: true });

const selectModel = (id) => {
  selectedModelId.value = id;
};

// 获取当前选中的员工
const getSelectedModel = () => {
  let selectedModel = null;

  if (checkedFixedModels.value.length > 0) {
    selectedModel = fixedModels.value.find(model => checkedFixedModels.value.includes(model.id));
    if (selectedModel) {
      return {
        ...selectedModel,
        type: 'custom',
        model_id: selectedModel.id.toString()
      };
    }
  } else if (checkedCustomModels.value.length > 0) {
    selectedModel = customModels.value.find(model => checkedCustomModels.value.includes(model.id));
    if (selectedModel) {
      return {
        ...selectedModel,
        type: 'custom',
        model_id: selectedModel.id.toString()
      };
    }
  }

  return null;
};

const toggleFixedCheck = (id) => {
  if (teamCollaboration.value) {
    // 团队协作模式下不允许单独切换固定员工
    return;
  }

  if (storyboardMode.value || digitalPersonMode.value) {
    // 分镜模式或数字人模式下不允许勾选员工
    return;
  }

  const index = checkedFixedModels.value.indexOf(id);
  if (index > -1) {
    checkedFixedModels.value.splice(index, 1);
  } else {
    checkedFixedModels.value = [id]; // 固定员工保持单选
    checkedCustomModels.value = [];
  }
};

const toggleCustomCheck = (id) => {
  if (teamCollaboration.value) {
    // 团队协作模式下不允许选择自建员工
    return;
  }

  if (storyboardMode.value || digitalPersonMode.value) {
    // 分镜模式或数字人模式下不允许勾选员工
    return;
  }

  const index = checkedCustomModels.value.indexOf(id);
  if (index > -1) {
    checkedCustomModels.value.splice(index, 1);
  } else {
    checkedCustomModels.value = [id]; // 自建员工保持单选
    checkedFixedModels.value = [];
  }
};

// 选择分镜模式工作模式
const selectStoryboardWorkMode = (mode) => {
  if (storyboardMode.value) {
    storyboardWorkMode.value = mode;
  }
};

const handleSendMessage = async () => {
  if (!userInput.value.trim() || isGenerating.value) return;

  // 检查分镜模式下是否选择了工作模式
  if (storyboardMode.value && !storyboardWorkMode.value) {
    ElMessage.warning('请先选择一个工作模式');
    return;
  }

  // 检查是否选择了员工（分镜模式和数字人模式下不需要选择员工）
  if (!storyboardMode.value && !digitalPersonMode.value) {
    const selectedModel = getSelectedModel();
    if (!selectedModel) {
      ElMessage.warning('请先选择一个员工');
      return;
    }
  }

  const userMessage = userInput.value.trim();
  const currentTime = new Date();

  // 添加用户消息
  const userMsg = {
    id: Date.now(),
    type: 'user',
    content: userMessage,
    timestamp: currentTime,
    nickname: userStore.userInfo?.nickname || '用户',
    avatar: userStore.userInfo?.avatar || '@/assets/image/ai-team/ai.png'
  };

  chatMessages.value.push(userMsg);
  userInput.value = "";

  // 滚动到底部
  scrollToBottom();

  isGenerating.value = true;

  try {
    // 如果是第一条消息，需要先获取标题和创建会话
    if (isFirstMessage.value) {
      // 在分镜模式或数字人模式下，获取选中的员工（可能为null）
      const selectedModel = storyboardMode.value || digitalPersonMode.value ? getSelectedModel() : getSelectedModel();
      await initializeChat(userMessage, selectedModel);
    }
    await sendStreamMessage(userMessage);
  } catch (error) {
    isGenerating.value = false;
  }
};

// 初始化聊天会话
const initializeChat = async (userMessage, selectedModel) => {
  try {
    // 第一步：获取对话标题
    const titleResponse = await post('/api/chat/generate/title', {
      text: userMessage
    }, { showError: true });

    if (titleResponse.code === 200) {
      currentChatTitle.value = titleResponse.data.title;
    }

    // 第二步：创建新的聊天会话
    // 根据分镜模式和工作模式确定type
    let chatType = 'custom'; // 默认类型

    if (selectedModel) {
      chatType = selectedModel.type;
    }

    if (storyboardMode.value && storyboardWorkMode.value) {
      if (storyboardWorkMode.value === 'auto') {
        chatType = 'storyboard_auto';
      } else if (storyboardWorkMode.value === 'supervised') {
        chatType = 'storyboard_handmade';
      }
    } else if (digitalPersonMode.value) {
      chatType = 'digital_person'; // 数字人模式的类型
    }

    // 构建请求体
    const requestBody = {
      type: chatType,
      title: currentChatTitle.value
    };

    // 只有在普通模式或团队协作模式下才传入model_id
    if (!storyboardMode.value && !digitalPersonMode.value) {
      if (selectedModel) {
        requestBody.model_id = selectedModel.model_id.toString();
      } else {
        requestBody.model_id = '1'; // 默认model_id
      }
    }
    // 分镜模式和数字人模式下不传入model_id

    const newChatResponse = await post('/api/chat/generate/new', requestBody, { showError: true });

    if (newChatResponse.code === 200) {
      currentChatId.value = newChatResponse.data.chat_id;
      currentChatType.value = chatType; // 更新当前聊天类型，用于右侧文字块显示
      isFirstMessage.value = false;

      // 通知父组件新聊天已创建，需要刷新左侧历史记录
      emit('new-chat-created', {
        chat_id: currentChatId.value,
        title: currentChatTitle.value,
        type: chatType
      });
    } else {
      throw new Error('创建聊天会话失败');
    }
  } catch (error) {
    throw error;
  }
};

// 发送流式消息
const sendStreamMessage = async (userMessage) => {
  if (!currentChatId.value) {
    throw new Error('聊天会话ID不存在');
  }

  // 获取当前选择的员工（分镜模式和数字人模式下可能为null）
  const selectedModel = getSelectedModel();
  if (!selectedModel && !storyboardMode.value && !digitalPersonMode.value) {
    throw new Error('未选择员工');
  }

  // 创建AI消息占位符
  const aiMsgId = Date.now() + 1;
  const aiMsg = {
    id: aiMsgId,
    type: 'ai',
    content: '',
    reasoningContent: '', // 思考内容
    timestamp: new Date(),
    nickname: 'AI',
    avatar: '@/assets/image/ai-team/ai.png',
    isStreaming: true,
    isThinking: false, // 是否正在思考
    thinkingComplete: false, // 思考是否完成
    showThinking: false, // 是否展开显示思考内容
    messageType: currentChatType.value // 记录消息创建时的聊天类型
  };

  chatMessages.value.push(aiMsg);
  scrollToBottom();

  try {
    // 获取认证token
    const token = Cookies.get('access_token');
    const headers = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = 'Bearer ' + token;
    }

    const requestBody = {
      chat_id: currentChatId.value,
      text: userMessage
    };

    // 只有在普通模式或团队协作模式下才传入model_id
    if (!storyboardMode.value && !digitalPersonMode.value) {
      if (selectedModel) {
        requestBody.model_id = selectedModel.model_id;
      } else {
        // 团队协作模式或普通模式下使用默认model_id
        requestBody.model_id = '1';
      }
    }
    // 分镜模式和数字人模式下不传入model_id

    const response = await fetch('/api/chat/generate/completions', {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 使用更简单的流式处理方法
    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          aiMsg.isStreaming = false;
          isGenerating.value = false;
          break;
        }

        // 解码数据块
        const chunk = decoder.decode(value, { stream: true });

        // 分割成行
        const lines = chunk.split('\n');

        for (const line of lines) {
          const trimmedLine = line.trim();

          if (trimmedLine.startsWith('data: ')) {
            const data = trimmedLine.slice(6);

            if (data === '[DONE]') {
              // 更新消息状态
              const msgIndex = chatMessages.value.findIndex(msg => msg.id === aiMsgId);
              if (msgIndex !== -1) {
                chatMessages.value[msgIndex] = {
                  ...chatMessages.value[msgIndex],
                  isStreaming: false
                };
              }
              isGenerating.value = false;
              scrollToBottom();
              return;
            }

            if (data && data.trim() !== '') {
              try {
                const parsed = JSON.parse(data);

                // 处理OpenAI兼容格式：choices[0].delta.content 和 reasoning_content
                let content = null;
                let reasoningContent = null;

                if (parsed.choices && parsed.choices.length > 0 && parsed.choices[0].delta) {
                  content = parsed.choices[0].delta.content;
                  reasoningContent = parsed.choices[0].delta.reasoning_content;
                } else if (parsed.content) {
                  // 兼容旧格式
                  content = parsed.content;
                }

                // 找到消息
                const msgIndex = chatMessages.value.findIndex(msg => msg.id === aiMsgId);
                if (msgIndex !== -1) {
                  const currentMsg = chatMessages.value[msgIndex];

                  // 处理思考内容
                  if (reasoningContent) {
                    // 如果有思考内容，设置思考状态并自动展开
                    if (!currentMsg.isThinking) {
                      chatMessages.value[msgIndex] = {
                        ...currentMsg,
                        isThinking: true,
                        thinkingComplete: false,
                        showThinking: true // 自动展开思考内容
                      };
                    }

                    // 更新思考内容
                    chatMessages.value[msgIndex] = {
                      ...chatMessages.value[msgIndex],
                      reasoningContent: chatMessages.value[msgIndex].reasoningContent + reasoningContent
                    };
                  }

                  // 处理正文内容
                  if (content) {
                    // 如果之前在思考，现在开始正文，标记思考完成
                    if (currentMsg.isThinking && !currentMsg.thinkingComplete) {
                      chatMessages.value[msgIndex] = {
                        ...chatMessages.value[msgIndex],
                        thinkingComplete: true,
                        isThinking: false
                      };
                    }

                    // 更新正文内容
                    chatMessages.value[msgIndex] = {
                      ...chatMessages.value[msgIndex],
                      content: chatMessages.value[msgIndex].content + content
                    };
                  }

                  // 强制触发Vue更新并滚动
                  await nextTick();
                  scrollToBottom();
                }

                // 检查是否完成
                if (parsed.choices && parsed.choices.length > 0 && parsed.choices[0].finish_reason) {
                  // 更新消息状态
                  const msgIndex = chatMessages.value.findIndex(msg => msg.id === aiMsgId);
                  if (msgIndex !== -1) {
                    chatMessages.value[msgIndex] = {
                      ...chatMessages.value[msgIndex],
                      isStreaming: false
                    };
                  }
                  isGenerating.value = false;
                  scrollToBottom();
                  return;
                }
              } catch {
              }
            }
          }
        }
      }
    } finally {
      // 确保最终状态正确
      const msgIndex = chatMessages.value.findIndex(msg => msg.id === aiMsgId);
      if (msgIndex !== -1) {
        chatMessages.value[msgIndex] = {
          ...chatMessages.value[msgIndex],
          isStreaming: false
        };
      }
      isGenerating.value = false;
      scrollToBottom();
    }

  } catch (error) {
    // 移除失败的AI消息
    const index = chatMessages.value.findIndex(msg => msg.id === aiMsgId);
    if (index > -1) {
      chatMessages.value.splice(index, 1);
    }
    throw error;
  }
};

// 新增：处理键盘事件
const handleKeyPress = (event) => {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    handleSendMessage();
  }
};

// 新增：处理文件按钮点击
const handleFileClick = () => {
  imageInputRef.value.click();
};

// 处理图片上传
const handleImageUpload = (event) => {
  const files = Array.from(event.target.files);

  files.forEach(file => {
    const validTypes = ["image/jpeg", "image/png", "image/jpg"];
    if (!validTypes.includes(file.type)) {
      ElMessage.error("请上传 JPG、JPEG 或 PNG 格式的图片");
      return;
    }

    if (file.size > 10 * 1024 * 1024) {
      ElMessage.error("图片大小不能超过10MB");
      return;
    }

    // 创建预览图片
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageData = {
        id: Date.now() + Math.random(),
        file: file,
        preview: e.target.result,
        name: file.name
      };
      uploadedImages.value.push(imageData);
    };
    reader.readAsDataURL(file);
  });

  // 清空input值，允许重复选择同一文件
  event.target.value = "";
};

// 删除图片
const removeImage = (imageId) => {
  const index = uploadedImages.value.findIndex(img => img.id === imageId);
  if (index > -1) {
    uploadedImages.value.splice(index, 1);
  }
};

// 预览图片
const previewImage = (imageId) => {
  const index = uploadedImages.value.findIndex(img => img.id === imageId);
  if (index > -1) {
    currentImageIndex.value = index;
    showImageViewer.value = true;
  }
};

// 关闭图片预览
const closeImageViewer = () => {
  showImageViewer.value = false;
};

// 新增：处理录音按钮点击
const handleRecordClick = () => {
  recordDialogVisible.value = true;
};

// 录音相关函数
const startRecording = async () => {
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

    // 设置音频上下文和分析器
    audioContext.value = new (window.AudioContext || window.webkitAudioContext)();
    analyser.value = audioContext.value.createAnalyser();
    microphone.value = audioContext.value.createMediaStreamSource(stream);

    analyser.value.fftSize = 256;
    microphone.value.connect(analyser.value);

    // 设置媒体录制器
    mediaRecorder.value = new MediaRecorder(stream);
    audioChunks.value = [];

    mediaRecorder.value.ondataavailable = (event) => {
      audioChunks.value.push(event.data);
    };

    mediaRecorder.value.onstop = () => {
      const audioBlob = new Blob(audioChunks.value, { type: 'audio/wav' });
      recordedAudioBlob.value = audioBlob;
    };

    // 开始录音
    mediaRecorder.value.start();
    isRecording.value = true;
    recordingTime.value = 0;

    // 开始计时
    recordingTimer.value = setInterval(() => {
      recordingTime.value++;
    }, 1000);

    // 开始波形动画
    updateWaveform();

    ElMessage.success('开始录音');
  } catch (error) {
    ElMessage.error('无法访问麦克风，请检查权限设置');
  }
};

const stopRecording = () => {
  if (mediaRecorder.value && isRecording.value) {
    mediaRecorder.value.stop();
    isRecording.value = false;

    // 停止计时
    if (recordingTimer.value) {
      clearInterval(recordingTimer.value);
      recordingTimer.value = null;
    }

    // 停止波形动画
    if (animationId.value) {
      cancelAnimationFrame(animationId.value);
      animationId.value = null;
    }

    // 关闭音频上下文
    if (audioContext.value) {
      audioContext.value.close();
      audioContext.value = null;
    }

    // 停止所有音频轨道
    if (microphone.value && microphone.value.mediaStream) {
      microphone.value.mediaStream.getTracks().forEach(track => track.stop());
    }

    ElMessage.success('录音已停止');
  }
};

const updateWaveform = () => {
  if (!analyser.value || !isRecording.value) return;

  const bufferLength = analyser.value.frequencyBinCount;
  const dataArray = new Uint8Array(bufferLength);
  analyser.value.getByteFrequencyData(dataArray);

  // 将频域数据转换为波形数据
  const newWaveformData = [];
  const step = Math.floor(bufferLength / 50);

  for (let i = 0; i < 50; i++) {
    let sum = 0;
    for (let j = 0; j < step; j++) {
      sum += dataArray[i * step + j];
    }
    const average = sum / step;
    // 将0-255的值映射到0-100的高度
    newWaveformData.push(Math.max(2, (average / 255) * 100));
  }

  waveformData.value = newWaveformData;

  animationId.value = requestAnimationFrame(updateWaveform);
};

const confirmRecording = async () => {
  if (!recordedAudioBlob.value) {
    ElMessage.error('没有录音数据');
    return;
  }

  try {
    isTranscribing.value = true;

    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', recordedAudioBlob.value, 'recording.wav');

    // 调用转录接口
    const response = await post('/api/covert/record', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      showError: true
    });

    if (response.code === 200 && response.data && response.data.text) {
      // 将转录结果写入输入框
      userInput.value = response.data.text;
      ElMessage.success('转录成功');

      // 关闭录音对话框
      recordDialogVisible.value = false;

      // 重置录音状态
      recordingTime.value = 0;
      waveformData.value = new Array(50).fill(0);
      recordedAudioBlob.value = null;
    } else {
      ElMessage.error(response.message || '转录失败');
    }
  } catch (error) {
    ElMessage.error('转录失败，请稍后重试');
  } finally {
    isTranscribing.value = false;
  }
};

const cancelRecording = () => {
  stopRecording();
  recordDialogVisible.value = false;
  recordingTime.value = 0;
  waveformData.value = new Array(50).fill(0);
  recordedAudioBlob.value = null;
  isTranscribing.value = false;
};

// 格式化录音时间
const formatTime = (seconds) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 新增：处理发送按钮点击
const handleSendClick = () => {
  handleSendMessage();
};

// 新建员工相关函数
const openCreateMemberDialog = () => {
  createMemberForm.value = {
    name: "",
    avatar: null,
    avatarFile: null,
    prompt: "",
    temperature: 0.7,
  };
  createMemberDialogVisible.value = true;
};

const triggerAvatarUpload = () => {
  avatarInputRef.value.click();
};

const handleAvatarUpload = async (event) => {
  const file = event.target.files[0];
  if (!file) return;

  const validTypes = ["image/jpeg", "image/png", "image/jpg"];
  if (!validTypes.includes(file.type)) {
    ElMessage.error("请上传 JPG、JPEG 或 PNG 格式的图片");
    return;
  }

  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error("图片大小不能超过5MB");
    return;
  }

  try {
    isUploadingAvatar.value = true;

    // 保存原始文件对象用于上传
    createMemberForm.value.avatarFile = file;

    // 创建预览图片
    const reader = new FileReader();
    reader.onload = (e) => {
      createMemberForm.value.avatar = e.target.result;
    };
    reader.readAsDataURL(file);
  } catch (error) {
    ElMessage.error("头像上传失败，请稍后重试");
  } finally {
    isUploadingAvatar.value = false;
    event.target.value = "";
  }
};

const createMember = async () => {
  if (!createMemberForm.value.name.trim()) {
    ElMessage.warning("请输入员工姓名");
    return;
  }

  if (!createMemberForm.value.avatarFile) {
    ElMessage.warning("请上传员工头像");
    return;
  }

  if (!createMemberForm.value.prompt.trim()) {
    ElMessage.warning("请输入提示词");
    return;
  }

  try {
    // 创建 FormData 对象
    const formData = new FormData();
    formData.append('name', createMemberForm.value.name.trim());
    formData.append('avatar', createMemberForm.value.avatarFile);
    formData.append('prompt', createMemberForm.value.prompt.trim());
    formData.append('temperature', createMemberForm.value.temperature.toString());

    const res = await post('/api/model/config/add', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      showError: true
    });

    if (res.code === 200) {
      ElMessage.success("员工创建成功");
      createMemberDialogVisible.value = false;

      emit("refresh-models");
    } else {
      ElMessage.error(res.message || "员工创建失败");
    }
  } catch (error) {
    ElMessage.error("员工创建失败，请稍后重试");
  }
};

// 删除员工函数
const deleteMember = async (id) => {
  try {
    ElMessageBox.confirm('确定要删除这个员工吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      const res = await del('/api/model/config/delete', { id: id }, {
        showError: true
      });
      if (res.code === 200) {
        ElMessage.success('删除成功');
        emit("refresh-models");
      } else {
        ElMessage.error(res.message || '删除失败');
      }
    }).catch(() => {
      // 用户取消删除
    });
  } catch (error) {
    ElMessage.error('删除失败，请稍后重试');
  }
};

// 自定义平滑滚动函数
const smoothScrollToBottom = (duration = 800) => {
  if (!chatContainer.value) return;

  const container = chatContainer.value;
  const startScrollTop = container.scrollTop;
  const targetScrollTop = container.scrollHeight - container.clientHeight;
  const distance = targetScrollTop - startScrollTop;

  if (distance === 0) return;

  const startTime = performance.now();

  const animateScroll = (currentTime) => {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // 使用缓动函数（ease-out）
    const easeOut = 1 - Math.pow(1 - progress, 3);

    container.scrollTop = startScrollTop + distance * easeOut;

    if (progress < 1) {
      requestAnimationFrame(animateScroll);
    }
  };

  requestAnimationFrame(animateScroll);
};

// 滚动到聊天底部
const scrollToBottom = (smooth = false) => {
  setTimeout(() => {
    if (chatContainer.value) {
      if (smooth) {
        // 使用自定义平滑滚动动画
        smoothScrollToBottom(800);
      } else {
        // 瞬间滚动（用于流式消息等需要快速跟随的场景）
        chatContainer.value.scrollTop = chatContainer.value.scrollHeight;
      }
    }
  }, 100);
};

// 滚动到聊天顶部
const scrollToTop = (smooth = true) => {
  setTimeout(() => {
    if (chatContainer.value) {
      if (smooth) {
        // 使用平滑滚动动画到顶部
        const container = chatContainer.value;
        const startScrollTop = container.scrollTop;
        const targetScrollTop = 0;
        const distance = targetScrollTop - startScrollTop;

        if (distance === 0) return;

        const duration = 600; // 滚动持续时间
        const startTime = performance.now();

        const animateScroll = (currentTime) => {
          const elapsed = currentTime - startTime;
          const progress = Math.min(elapsed / duration, 1);

          // 使用缓动函数（ease-out）
          const easeOut = 1 - Math.pow(1 - progress, 3);

          container.scrollTop = startScrollTop + distance * easeOut;

          if (progress < 1) {
            requestAnimationFrame(animateScroll);
          }
        };

        requestAnimationFrame(animateScroll);
      } else {
        // 瞬间滚动到顶部
        chatContainer.value.scrollTop = 0;
      }
    }
  }, 100);
};

// 格式化时间显示
const formatChatTime = (timestamp) => {
  const now = new Date();
  const msgTime = new Date(timestamp);
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const msgDate = new Date(msgTime.getFullYear(), msgTime.getMonth(), msgTime.getDate());

  return msgTime.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取类型显示文本
const getTypeText = (type) => {
  switch (type) {
    case 'custom':
      return '闲聊状态';
    case 'storyboard_auto':
      return '自动工作';
    case 'storyboard_handmade':
      return '监督工作';
    case 'digital_person':
      return '数字人模式';
    case 'teamwork':
      return '团队协作';
    default:
      return '其他状态';
  }
};

// 切换思考内容显示
const toggleThinking = (messageId) => {
  const msgIndex = chatMessages.value.findIndex(msg => msg.id === messageId);
  if (msgIndex !== -1) {
    chatMessages.value[msgIndex] = {
      ...chatMessages.value[msgIndex],
      showThinking: !chatMessages.value[msgIndex].showThinking
    };
  }
};

// 复制消息内容
const copyMessage = (content) => {
  navigator.clipboard.writeText(content).then(() => {
    ElMessage.success('已复制到剪贴板');
  }).catch(() => {
    ElMessage.error('复制失败');
  });
};

// 编辑消息（用户消息）
const editMessage = (messageId) => {
  const message = chatMessages.value.find(msg => msg.id === messageId);
  if (message && message.type === 'user') {
    userInput.value = message.content;
    ElMessage.info('消息内容已填入输入框');
  }
};

// 监督模式按钮处理函数
const handleGenerateReference = (messageId) => {
  console.log('生成参考图', messageId);
  ElMessage.info('生成参考图功能开发中...');
};

const handleManualEdit = (messageId) => {
  console.log('人工编辑', messageId);
  ElMessage.info('人工编辑功能开发中...');
};

const handleAIOptimize = (messageId) => {
  currentOptimizeMessageId.value = messageId;
  aiOptimizeInput.value = '';
  aiOptimizeDialogVisible.value = true;
};

// AI优化发送处理
const handleAIOptimizeSend = async () => {
  if (!aiOptimizeInput.value.trim() || isGenerating.value) return;

  // 将优化输入内容设置到主输入框
  const optimizeContent = aiOptimizeInput.value.trim();

  // 关闭模态框
  aiOptimizeDialogVisible.value = false;
  aiOptimizeInput.value = '';
  currentOptimizeMessageId.value = null;

  // 设置主输入框内容并发送
  userInput.value = optimizeContent;

  // 复用现有的发送消息逻辑
  await handleSendMessage();
};

// AI优化输入框键盘事件
const handleAIOptimizeKeyPress = (event) => {
  if (event.key === "Enter" && !event.shiftKey) {
    event.preventDefault();
    handleAIOptimizeSend();
  }
};

const handleStartTask = async (messageId) => {
  console.log('开始任务', messageId);

  // 找到对应的消息
  const message = chatMessages.value.find(msg => msg.id === messageId);
  if (!message || message.type !== 'ai') {
    ElMessage.error('未找到对应的AI消息');
    return;
  }

  // 检查是否有当前聊天ID
  if (!currentChatId.value) {
    ElMessage.error('当前聊天会话不存在');
    return;
  }

  try {
    // 调用开始任务接口
    const response = await post('/api/storyboard/make/start', {
      chat_id: currentChatId.value
    }, { showError: true });

    // 检查接口返回的code是否为200
    if (response.code === 200) {
      // 接口调用成功，传递内容到右侧面板
      const content = message.content || '';
      isTaskText.value = "任务正在处理中..."
      startTask.value = true;

      // 移除自动滚动到顶部的功能
      // scrollToTop(false);

      emit('start-task', content);
      ElMessage.success('任务已开始，内容已发送到右侧面板');
    } else {
      // 接口调用失败
      ElMessage.error(response.message || '开始任务失败');
    }
  } catch {
  }
};

// 判断是否是最新的AI消息
const isLatestAIMessage = (messageId) => {
  // 找到所有AI消息
  const aiMessages = chatMessages.value.filter(msg => msg.type === 'ai');
  if (aiMessages.length === 0) return false;

  // 返回是否是最后一个AI消息
  const lastAIMessage = aiMessages[aiMessages.length - 1];
  return lastAIMessage.id === messageId;
};

// 获取最新AI消息的聊天类型
const getLatestAIMessageChatType = () => {
  // 找到所有AI消息
  const aiMessages = chatMessages.value.filter(msg => msg.type === 'ai');
  if (aiMessages.length === 0) return null;

  // 获取最后一个AI消息的类型
  const lastAIMessage = aiMessages[aiMessages.length - 1];
  return lastAIMessage.messageType || null;
};

// 重新生成AI回复
const regenerateAIResponse = async (messageId) => {
  if (isGenerating.value) return;

  const messageIndex = chatMessages.value.findIndex(msg => msg.id === messageId);
  if (messageIndex === -1) return;

  const message = chatMessages.value[messageIndex];
  if (message.type !== 'ai') return;

  // 找到上一条用户消息
  let userMessage = '';
  for (let i = messageIndex - 1; i >= 0; i--) {
    if (chatMessages.value[i].type === 'user') {
      userMessage = chatMessages.value[i].content;
      break;
    }
  }

  if (!userMessage) {
    ElMessage.error('未找到对应的用户消息');
    return;
  }

  if (!currentChatId.value) {
    ElMessage.error('聊天会话ID不存在');
    return;
  }

  isGenerating.value = true;

  // 设置加载状态和动画
  message.content = '正在重新回复内容';
  message.isStreaming = true;
  message.isRegenerating = true;

  // 添加动画效果的点
  let dotCount = 0;
  const animationInterval = setInterval(() => {
    if (!message.isRegenerating) {
      clearInterval(animationInterval);
      return;
    }
    dotCount = (dotCount + 1) % 4;
    message.content = '正在重新回复内容' + '.'.repeat(dotCount);
  }, 500);

  try {
    // 获取当前选择的员工
    const selectedModel = getSelectedModel();
    if (!selectedModel) {
      ElMessage.error('未选择员工');
      isGenerating.value = false;
      return;
    }

    // 获取认证token
    const token = Cookies.get('access_token');
    const headers = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = 'Bearer ' + token;
    }

    const response = await fetch('/api/chat/generate/completions', {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({
        chat_id: currentChatId.value,
        text: userMessage,
        refresh: true,
        model_id: selectedModel.model_id
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 清除动画并重置内容
    clearInterval(animationInterval);
    message.content = '';
    message.reasoningContent = '';
    message.isRegenerating = false;
    message.isStreaming = true;
    message.isThinking = false;
    message.thinkingComplete = false;
    message.showThinking = false; // 重置展开状态，等待新的思考内容自动展开
    message.timestamp = new Date();

    // 使用流式处理方法
    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          message.isStreaming = false;
          isGenerating.value = false;
          break;
        }

        // 解码数据块
        const chunk = decoder.decode(value, { stream: true });

        // 分割成行
        const lines = chunk.split('\n');

        for (const line of lines) {
          const trimmedLine = line.trim();

          if (trimmedLine.startsWith('data: ')) {
            const data = trimmedLine.slice(6);

            if (data === '[DONE]') {
              message.isStreaming = false;
              isGenerating.value = false;
              scrollToBottom();
              return;
            }

            if (data && data.trim() !== '') {
              try {
                const parsed = JSON.parse(data);

                // 处理OpenAI兼容格式：choices[0].delta.content 和 reasoning_content
                let content = null;
                let reasoningContent = null;

                if (parsed.choices && parsed.choices.length > 0 && parsed.choices[0].delta) {
                  content = parsed.choices[0].delta.content;
                  reasoningContent = parsed.choices[0].delta.reasoning_content;
                } else if (parsed.content) {
                  // 兼容旧格式
                  content = parsed.content;
                }

                // 处理思考内容
                if (reasoningContent) {
                  // 如果有思考内容，设置思考状态并自动展开
                  if (!message.isThinking) {
                    message.isThinking = true;
                    message.thinkingComplete = false;
                    message.showThinking = true; // 自动展开思考内容
                  }

                  // 更新思考内容
                  message.reasoningContent += reasoningContent;
                }

                // 处理正文内容
                if (content) {
                  // 如果之前在思考，现在开始正文，标记思考完成
                  if (message.isThinking && !message.thinkingComplete) {
                    message.thinkingComplete = true;
                    message.isThinking = false;
                  }

                  // 更新正文内容
                  message.content += content;
                }

                // 强制触发Vue更新并滚动
                await nextTick();
                scrollToBottom();

                // 检查是否完成
                if (parsed.choices && parsed.choices.length > 0 && parsed.choices[0].finish_reason) {
                  message.isStreaming = false;
                  isGenerating.value = false;
                  scrollToBottom();
                  return;
                }
              } catch {
                // 忽略解析错误
              }
            }
          }
        }
      }
    } finally {
      // 确保最终状态正确
      message.isStreaming = false;
      isGenerating.value = false;
      scrollToBottom();
    }

  } catch (error) {
    // 清除动画
    clearInterval(animationInterval);
    message.isRegenerating = false;
    message.isStreaming = false;
    message.content = '重新生成失败，请稍后重试';
    isGenerating.value = false;
    ElMessage.error('重新生成失败，请稍后重试');
  }
};

// 清空聊天消息
const clearChat = () => {
  chatMessages.value = [];
  userInput.value = "";
  uploadedImages.value = [];
  isGenerating.value = false;

  // 重置聊天会话状态
  currentChatId.value = null;
  currentChatTitle.value = '';
  currentChatType.value = '';
  isFirstMessage.value = true;
  isLoadingChatHistory.value = false;
};

// 加载聊天历史记录
const loadChatHistory = async (chatItem) => {
  // 清空当前聊天
  clearChat();

  // 设置聊天会话信息
  currentChatId.value = chatItem.chat_id;
  currentChatTitle.value = chatItem.title;
  currentChatType.value = chatItem.type;
  isFirstMessage.value = false;

  // 根据聊天类型设置对应的UI状态
  if (chatItem.type === 'storyboard_auto') {
    storyboardMode.value = true;
    storyboardWorkMode.value = 'auto';
    digitalPersonMode.value = false;
    teamCollaboration.value = false;
  } else if (chatItem.type === 'storyboard_handmade') {
    storyboardMode.value = true;
    storyboardWorkMode.value = 'supervised';
    digitalPersonMode.value = false;
    teamCollaboration.value = false;
  } else if (chatItem.type === 'digital_person') {
    storyboardMode.value = false;
    storyboardWorkMode.value = '';
    digitalPersonMode.value = true;
    teamCollaboration.value = false;
  } else if (chatItem.type === 'teamwork') {
    storyboardMode.value = false;
    storyboardWorkMode.value = '';
    digitalPersonMode.value = false;
    teamCollaboration.value = true;
  } else {
    // 默认状态（custom或其他）
    storyboardMode.value = false;
    storyboardWorkMode.value = '';
    digitalPersonMode.value = false;
    teamCollaboration.value = false;
  }

  // 设置加载状态
  isLoadingChatHistory.value = true;

  try {
    // 调用API获取聊天详情
    const response = await get('/api/chat/generate/detail', { chat_id: chatItem.chat_id });

    if (response.code === 200 && response.data && response.data.chat) {
      const chatData = response.data.chat;

      // 将API返回的聊天记录转换为组件需要的格式
      const historyMessages = chatData.map((item, index) => {
        // 判断是否为AI消息（支持 'ai' 和 'assistant' 两种角色）
        const isAI = item.role === 'ai' || item.role === 'assistant';

        return {
          id: Date.now() + index,
          type: item.role === 'user' ? 'user' : 'ai',
          content: item.content,
          reasoningContent: isAI ? (item.reasoning_content || '') : '', // 历史思考内容
          timestamp: new Date(item.time),
          nickname: item.role === 'user' ? userStore.getUserData.nickname : 'AI',
          avatar: item.role === 'user' ? userStore.getUserData.avatar : '@/assets/image/ai-team/ai.png',
          isStreaming: false,
          isThinking: false,
          thinkingComplete: isAI && item.reasoning_content ? true : false, // 历史消息如果有思考内容则标记为已完成
          showThinking: isAI && item.reasoning_content ? true : false, // 历史消息如果有思考内容则自动展开
          messageType: isAI ? chatItem.type : null // AI消息记录聊天类型
        };
      });

      // 设置聊天消息
      chatMessages.value = historyMessages;

      // 平滑滚动到底部
      setTimeout(() => {
        scrollToBottom(true);
      }, 200);

    } else {
      ElMessage.error(response.message || '获取聊天详情失败');
    }
  } catch (error) {
    ElMessage.error('加载聊天历史失败，请稍后重试');
  } finally {
    // 无论成功还是失败，都要重置加载状态
    isLoadingChatHistory.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  clearChat,
  loadChatHistory
});

// 组件卸载时清理资源
onUnmounted(() => {
  if (recordingTimer.value) {
    clearInterval(recordingTimer.value);
  }
  if (animationId.value) {
    cancelAnimationFrame(animationId.value);
  }
  if (audioContext.value) {
    audioContext.value.close();
  }
});
</script>

<template>
  <div class="flex flex-col gap-y-3 w-full">
    <div class="h-[54.3vh] bg-[#1B2130] border border-[#393B4A] p-[0.8333vw] flex flex-col relative">
      <!-- 顶部标题栏 -->
      <div class="flex items-center gap-[0.6vw] mb-[0.8vw]">
        <!-- 左侧标题 -->
        <span class="text-white text-[1vw]">
          {{ currentChatTitle || 'AI团队对话' }}
        </span>

        <!-- 右侧文字块 -->
        <div class="bg-[#303A54] text-[#C4C4C4] text-[0.6vw] px-[0.3vw] py-[0.06vw] rounded-[4px]">
          {{ currentChatType ? getTypeText(currentChatType) : '初始化' }}
        </div>
      </div>

      <!-- 聊天消息区域 -->
      <div
        ref="chatContainer"
        class="flex-1 space-y-[1vw] pr-[0.5vw] chat-scroll-hidden relative"
        :class="{
          'overflow-y-scroll overflow-x-hidden': isTaskText !== '任务正在处理中...',
          'overflow-hidden': isTaskText === '任务正在处理中...'
        }"
      >

        <div v-if="chatMessages.length === 0" class="flex items-center justify-center h-full">
          <span class="text-[#8B94A3] text-[0.9vw]">
            {{ isLoadingChatHistory ? '正在加载消息内容...' : '开始与AI团队对话吧...' }}
          </span>
        </div>

        <!-- 聊天消息列表 -->
        <div
          v-for="message in chatMessages"
          :key="message.id"
          class="flex gap-[0.8vw]"
          :class="message.type === 'user' ? 'flex-row-reverse' : 'flex-row'"
        >
          <!-- 头像 -->
          <div class="flex-shrink-0">
            <img
              :src="userStore.getUserData.avatar"
              class="w-[2.5vw] h-[2.5vw] rounded-lg object-cover border border-[#393B4A]"
              loading="lazy"
            />
          </div>

          <!-- 消息主体 -->
          <div class="flex flex-col max-w-[70%]" :class="message.type === 'user' ? 'items-end' : 'items-start'">
            <!-- 时间和昵称 -->
            <div
              class="flex items-center gap-[0.5vw] mb-[0.3vw]"
              :class="message.type === 'user' ? 'flex-row-reverse' : 'flex-row'"
            >
              <span class="text-[#FFFFFF] text-[0.8vw]">{{ message.type === 'user' ? userStore.getUserData.nickname : 'AI' }}</span>
              <span class="text-[#8B94A3] text-[0.7vw]">{{ formatChatTime(message.timestamp) }}</span>
            </div>

            <!-- 消息内容 -->
            <div class="w-fit mb-[0.5vw]">
              <!-- AI消息的思考块 -->
              <div
                v-if="message.type === 'ai' && (message.reasoningContent || message.isThinking)"
                class="mb-[0.3vw]"
              >
                <!-- 思考状态切换按钮 -->
                <div
                  class="flex items-center gap-[0.3vw] cursor-pointer hover:opacity-80 transition-opacity duration-200"
                  @click="toggleThinking(message.id)"
                >
                  <div class="w-[2px] bg-[#8B94A3] flex-shrink-0" :style="{ height: message.showThinking ? 'auto' : '1.2em' }"></div>
                  <span class="text-[#8B94A3] text-[0.7vw]">
                    <template v-if="message.isThinking">
                      思考中<span class="thinking-dots">...</span>
                    </template>
                    <template v-else-if="message.thinkingComplete || (message.reasoningContent && !message.isStreaming)">
                      已完成思考!
                    </template>
                    <template v-else>
                      思考内容
                    </template>
                  </span>
                  <!-- 展开/收缩图标 -->
                  <img
                    :src="message.showThinking ? contractionIcon : expandIcon"
                    :alt="message.showThinking ? '收缩' : '展开'"
                    class="w-[1vw] h-[1vw] ml-[0.3vw] transition-transform duration-200"
                    loading="lazy"
                  />
                </div>

                <!-- 思考内容展开区域 -->
                <div
                  v-if="message.showThinking && (message.reasoningContent || message.isThinking)"
                  class="mt-[0.2vw] flex"
                >
                  <div class="w-[2px] bg-[#8B94A3] flex-shrink-0 mr-[0.5vw]"></div>
                  <div
                    class="flex-1 text-[#C4C4C4] text-[0.7vw] leading-relaxed opacity-80 bg-[#1F2937] p-[0.4vw] rounded-[6px]"
                    v-html="escapeHtml(message.reasoningContent || '').replace(/\n/g, '<br>')"
                  ></div>
                </div>
              </div>

              <!-- 正常消息内容 - 只有在非思考状态或思考完成后才显示 -->
              <div
                v-if="message.type === 'user' || !message.isThinking || message.thinkingComplete || message.content"
                class="p-[0.5vw] text-white text-[0.8vw] leading-relaxed w-fit markdown-content relative"
                :class="message.type === 'user' ? 'bg-[#273047] rounded-l-[8px] rounded-br-[8px] rounded-tr-[0px]' : 'bg-[#273047] rounded-r-[8px] rounded-bl-[8px] rounded-tl-[0px]'"
              >
                <!-- 消息内容 -->
                <div v-html="renderMessageContent(message)"></div>
                <!-- 流式响应打字机光标 -->
                <span
                  v-if="message.type === 'ai' && message.isStreaming && (!message.isThinking || message.thinkingComplete)"
                  class="inline-block w-[2px] h-[1em] bg-white ml-1 animate-pulse"
                ></span>
              </div>
            </div>

            <!-- 功能按钮 -->
            <div
              class="flex items-center gap-[0.5vw] mb-[0.5vw]"
              :class="message.type === 'user' ? 'flex-row-reverse' : 'flex-row'"
            >
              <!-- 用户消息按钮 -->
              <template v-if="message.type === 'user'">
                <img
                  @click="copyMessage(message.content)"
                  src="@/assets/image/ai-team/copy.png"
                  alt="复制"
                  class="w-[1.4vw] h-[1.4vw] cursor-pointer transition-opacity duration-200"
                  loading="lazy"
                />
                <img
                  @click="editMessage(message.id)"
                  src="@/assets/image/ai-team/edit.png"
                  alt="编辑"
                  class="w-[1.4vw] h-[1.4vw] cursor-pointer transition-opacity duration-200"
                  loading="lazy"
                />
              </template>

              <!-- AI消息按钮 -->
              <template v-else>
                <!-- 监督模式下的特殊按钮 - 基于最新AI消息的类型判断 -->
                <template v-if="getLatestAIMessageChatType() === 'storyboard_handmade' && !message.isStreaming && isLatestAIMessage(message.id)">
                  <div class="flex items-center gap-[0.5vw]">
                    <!-- 生成参考图 -->
                    <button
                      @click="handleGenerateReference(message.id)"
                      class="bg-[#303A54] border border-[#8B94A3] text-[#FFFFFF] text-[0.7vw] px-[0.8vw] py-[0.2vw] rounded-md hover:bg-[#3A4451] hover:text-white transition-all duration-200"
                      :class="{ 'opacity-50 cursor-not-allowed': isGenerating }"
                      :disabled="isGenerating"
                    >
                      生成参考图
                    </button>

                    <!-- 人工编辑 -->
                    <button
                      @click="handleManualEdit(message.id)"
                      class="bg-[#303A54] border border-[#8B94A3] text-[#FFFFFF] text-[0.7vw] px-[0.8vw] py-[0.2vw] rounded-md hover:bg-[#3A4451] hover:text-white transition-all duration-200"
                      :class="{ 'opacity-50 cursor-not-allowed': isGenerating }"
                      :disabled="isGenerating"
                    >
                      人工编辑
                    </button>

                    <!-- AI优化 -->
                    <button
                      @click="handleAIOptimize(message.id)"
                      class="bg-[#303A54] border border-[#8B94A3] text-[#FFFFFF] text-[0.7vw] px-[0.8vw] py-[0.2vw] rounded-md hover:bg-[#3A4451] hover:text-white transition-all duration-200"
                      :class="{ 'opacity-50 cursor-not-allowed': isGenerating }"
                      :disabled="isGenerating"
                    >
                      AI优化
                    </button>

                    <!-- 开始任务 -->
                    <button
                      @click="handleStartTask(message.id)"
                      class="bg-[#223380] text-white rounded-md text-[0.7vw] py-[0.2vw] px-[0.8vw] flex items-center justify-center gap-[0.2vw]"
                      style="
                        box-shadow: 0px 2px 6px 0px rgba(86, 122, 214, 0.6),
                          inset -1px -1px 0px 0px #3354e8;
                      "
                      :class="{ 'opacity-50 cursor-not-allowed': isGenerating }"
                      :disabled="isGenerating || startTask"
                    >
                      <img
                        src="@/assets/image/ai-team/start.png"
                        alt="开始"
                        class="w-[0.6vw] h-[0.6vw] flex-shrink-0"
                        loading="lazy"
                      />
                      <span class="flex-shrink-0">{{ isTaskText }}</span>
                    </button>
                  </div>
                </template>

                <!-- 分镜模式自动工作下的按钮 - 基于最新AI消息的类型判断 -->
                <template v-else-if="getLatestAIMessageChatType() === 'storyboard_auto' && !message.isStreaming && isLatestAIMessage(message.id)">
                  <!-- 这里可以添加自动工作模式下的特殊按钮，目前暂时不显示任何按钮 -->
                </template>

                <!-- 非分镜模式下的默认按钮 - 基于最新AI消息的类型判断 -->
                <template v-else-if="getLatestAIMessageChatType() !== 'storyboard_handmade' && getLatestAIMessageChatType() !== 'storyboard_auto'">
                  <img
                    @click="regenerateAIResponse(message.id)"
                    src="@/assets/image/ai-team/regenerate.png"
                    alt="重新生成"
                    class="w-[1.4vw] h-[1.4vw] cursor-pointer transition-opacity duration-200"
                    :class="{ 'opacity-30 cursor-not-allowed': isGenerating}"
                    loading="lazy"
                  />
                  <img
                    @click="copyMessage(message.content)"
                    src="@/assets/image/ai-team/copy.png"
                    alt="复制"
                    class="w-[1.4vw] h-[1.4vw] cursor-pointer transition-opacity duration-200"
                    loading="lazy"
                  />
                </template>
              </template>
            </div>
          </div>
        </div>
      </div>

      <!-- 任务处理遮罩 - 覆盖整个聊天区域 -->
      <div
        v-if="isTaskText === '任务正在处理中...'"
        class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 pointer-events-auto"
        @click.stop
        @wheel.prevent
        @touchmove.prevent
        @scroll.prevent
      >
        <div class="bg-[#1B2130] border border-[#393B4A] rounded-none px-[2vw] py-[1.5vw] text-center">
          <span class="text-white text-[1.2vw]">
            正在进行图像处理中
            <span class="loading-dots">
              <span class="dot1">.</span><span class="dot2">.</span><span class="dot3">.</span>
            </span>
          </span>
        </div>
      </div>
    </div>

    <div class="h-[13vh] bg-[#1B2130] border border-[#393B4A]">
      <!-- Middle section content goes here -->
      <div class="flex h-full w-full">
        <!-- Left section (20.49%) -->
        <div class="w-[20.49%] h-full p-[0.5vw] box-border overflow-hidden">
          <div class="flex flex-col h-full justify-center">
            <!-- 分镜模式容器 -->
            <div
              class="h-[calc(50%-0.2vw)] max-h-[34px] border p-[0.4vw] rounded-lg flex items-center justify-between box-border overflow-hidden relative"
              :class="{
                'border-[#748FD4] bg-[#202740]': storyboardMode,
                'border-[#707887] bg-[#37415C]': !storyboardMode,
                'cursor-pointer': isTaskText !== '任务正在处理中...',
                'cursor-not-allowed opacity-50': isTaskText === '任务正在处理中...'
              }"
              @click="isTaskText !== '任务正在处理中...' ? (storyboardMode = !storyboardMode) : null"
            >
              <el-switch
                v-model="storyboardMode"
                size="small"
                active-text="分镜模式"
                :disabled="isTaskText === '任务正在处理中...'"
                :style="{
                  '--el-switch-on-color': '#1677FF',
                  '--el-switch-off-color': '#BFBFBF',
                  '--el-text-color-primary': storyboardMode
                    ? '#FFFFFF'
                    : '#C4C4C4',
                }"
                @click.stop
              />

              <!-- 分镜模式工作模式选择 -->
              <el-tooltip
                v-if="storyboardMode"
                placement="right"
                popper-class="custom-add-image"
                trigger="click"
                effect="customadd"
                :hide-after="0"
                :show-after="0"
                :disabled="isTaskText === '任务正在处理中...'"
              >
                <template #default>
                  <div
                    class="w-[12px] h-[12px] bg-[#303A54] border border-[#8B94A3] rounded-full flex items-center justify-center transition-colors duration-200"
                    :class="{
                      'bg-[#748FD4] border-[#748FD4]': storyboardWorkMode,
                      'cursor-pointer hover:bg-[#3A4451]': isTaskText !== '任务正在处理中...',
                      'cursor-not-allowed opacity-50': isTaskText === '任务正在处理中...'
                    }"
                    @click.stop="isTaskText !== '任务正在处理中...' ? null : null"
                  >
                    <span class="text-white text-[8px]" v-if="!storyboardWorkMode">?</span>
                    <span class="text-white text-[6px]" v-else>✓</span>
                  </div>
                </template>
                <template #content>
                  <div
                    class="text-[0.75vw] leading-tight py-[0.1vw] px-[0.5vw] transition-colors duration-200"
                    :class="{
                      'text-[#748FD4] bg-[#202740]': storyboardWorkMode === 'auto',
                      'text-[#C4C4C4] hover:text-white cursor-pointer': storyboardWorkMode !== 'auto' && isTaskText !== '任务正在处理中...',
                      'text-[#8B94A3] cursor-not-allowed': isTaskText === '任务正在处理中...'
                    }"
                    @click="isTaskText !== '任务正在处理中...' ? selectStoryboardWorkMode('auto') : null"
                  >
                    自动工作
                  </div>
                  <div
                    class="text-[0.75vw] leading-tight py-[0.1vw] px-[0.5vw] mt-1 transition-colors duration-200"
                    :class="{
                      'text-[#748FD4] bg-[#202740]': storyboardWorkMode === 'supervised',
                      'text-[#C4C4C4] hover:text-white cursor-pointer': storyboardWorkMode !== 'supervised' && isTaskText !== '任务正在处理中...',
                      'text-[#8B94A3] cursor-not-allowed': isTaskText === '任务正在处理中...'
                    }"
                    @click="isTaskText !== '任务正在处理中...' ? selectStoryboardWorkMode('supervised') : null"
                  >
                    监督工作
                  </div>
                </template>
              </el-tooltip>
            </div>

            <!-- 间距 -->
            <div class="h-[0.7vw]"></div>

            <!-- 数字人模式容器 -->
            <div
              class="h-[calc(50%-0.2vw)] max-h-[34px] border p-[0.4vw] rounded-lg flex items-center box-border overflow-hidden"
              :class="{
                'border-[#748FD4] bg-[#202740]': digitalPersonMode,
                'border-[#707887] bg-[#37415C]': !digitalPersonMode,
                'cursor-pointer': isTaskText !== '任务正在处理中...',
                'cursor-not-allowed opacity-50': isTaskText === '任务正在处理中...'
              }"
              @click="isTaskText !== '任务正在处理中...' ? (digitalPersonMode = !digitalPersonMode) : null"
            >
              <el-switch
                v-model="digitalPersonMode"
                size="small"
                active-text="数字人模式"
                :disabled="isTaskText === '任务正在处理中...'"
                :style="{
                  '--el-switch-on-color': '#1677FF',
                  '--el-switch-off-color': '#BFBFBF',
                  '--el-text-color-primary': digitalPersonMode
                    ? '#FFFFFF'
                    : '#C4C4C4',
                }"
                @click.stop
              />
            </div>
          </div>
        </div>

        <!-- Divider -->
        <div class="w-[1px] h-full bg-[#393B4A]"></div>

        <!-- Middle section (45.49%) -->
        <div class="w-[45.49%] h-full">
          <div class="flex flex-col h-full">
            <div class="flex-none">
              <div class="flex flex-row gap-x-3 items-center">
                <span class="ml-3 mt-1 text-[0.9vw]">固定员工</span>
                <!-- 团队协作容器 -->
                <div
                  class="mt-2 h-[24px] border px-[0.4vw] py-[0.1vw] rounded-lg flex items-center box-border overflow-hidden"
                  :class="{
                    'border-[#748FD4] bg-[#202740]': teamCollaboration,
                    'border-[#707887] bg-[#37415C]': !teamCollaboration,
                    'cursor-pointer': isTaskText !== '任务正在处理中...',
                    'cursor-not-allowed opacity-50': isTaskText === '任务正在处理中...'
                  }"
                  @click="isTaskText !== '任务正在处理中...' ? (teamCollaboration = !teamCollaboration) : null"
                >
                  <el-switch
                    v-model="teamCollaboration"
                    size="small"
                    active-text="团队协作"
                    :disabled="isTaskText === '任务正在处理中...'"
                    :style="{
                      '--el-switch-on-color': '#1677FF',
                      '--el-switch-off-color': '#BFBFBF',
                      '--el-text-color-primary': teamCollaboration
                        ? '#FFFFFF'
                        : '#C4C4C4',
                    }"
                    @click.stop
                  />
                </div>
              </div>
            </div>
            <div
              class="flex-grow overflow-x-auto overflow-y-hidden flex justify-start scroll-container"
              ref="scrollContainer"
            >
              <div class="flex gap-x-2 h-full py-[0.7vh] mx-2">
                <div
                  v-for="model in fixedModels"
                  :key="model.id"
                  class="flex-shrink-0 aspect-square h-[100%] bg-[#303A54] overflow-hidden border border-[#8B94A3] transition-colors duration-200 relative"
                  :class="{
                    'cursor-pointer hover:border-[#748FD4]': !teamCollaboration && !storyboardMode && !digitalPersonMode && isTaskText !== '任务正在处理中...',
                    'cursor-not-allowed opacity-50': teamCollaboration || storyboardMode || digitalPersonMode || isTaskText === '任务正在处理中...'
                  }"
                  @click="isTaskText !== '任务正在处理中...' ? selectModel(model.id) : null"
                  @click.stop="isTaskText !== '任务正在处理中...' ? toggleFixedCheck(model.id) : null"
                >
                  <div
                    class="absolute top-[2px] right-[2px] w-[1vw] h-[1vw] bg-white bg-opacity-60 flex items-center justify-center"
                    :class="{
                      'border-2 border-white': !checkedFixedModels.includes(model.id),
                      'opacity-50': teamCollaboration && !checkedFixedModels.includes(model.id),
                      'opacity-30': storyboardMode || digitalPersonMode || isTaskText === '任务正在处理中...',
                      'cursor-pointer': isTaskText !== '任务正在处理中...',
                      'cursor-not-allowed': isTaskText === '任务正在处理中...'
                    }"
                  >
                    <img
                      v-if="checkedFixedModels.includes(model.id)"
                      src="@/assets/image/ai-team/gou.png"
                      class="w-full h-full object-contain"
                      loading="lazy"
                    />
                  </div>
                  <img
                    :src="model.avatar"
                    :alt="model.name"
                    class="w-full h-full object-cover"
                    loading="lazy"
                  />
                  <div
                    class="absolute bottom-0 left-0 right-0 h-[30%] bg-[#000000] bg-opacity-60 flex items-center justify-center text-white text-[1.5vh]"
                  >
                    {{ model.name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Divider -->
        <div class="w-[1px] h-full bg-[#393B4A]"></div>

        <!-- Right section (34.00%) -->
        <div class="w-[34.00%] h-full">
          <div class="flex h-full">
            <!-- Left part (65%) -->
            <div class="h-full w-[100%]">
              <div class="flex flex-col h-full">
                <div class="flex-none">
                  <div class="flex flex-row gap-x-3 items-center">
                    <span class="ml-3 mt-1 text-[0.9vw]">自建员工</span>
                    <!-- 占位元素，保持与中间区域相同的高度 -->
                    <div
                      class="mt-2 h-[24px] px-[0.4vw] py-[0.1vw] flex items-center box-border opacity-0"
                    >
                      <span class="text-[12px]">占位</span>
                    </div>
                  </div>
                </div>
                <div
                  class="flex-grow overflow-x-auto overflow-y-hidden flex justify-start scroll-container"
                  ref="scrollContainer2"
                >
                  <div class="flex gap-x-2 h-full py-[0.7vh] mx-2">
                    <div
                      v-for="model in customModels"
                      :key="'custom-' + model.id"
                      class="flex-shrink-0 aspect-square h-[100%] bg-[#303A54] overflow-hidden border border-[#8B94A3] transition-colors duration-200 relative"
                      :class="{
                        'cursor-pointer hover:border-[#748FD4]': !teamCollaboration && !storyboardMode && !digitalPersonMode && isTaskText !== '任务正在处理中...',
                        'cursor-not-allowed opacity-50': teamCollaboration || storyboardMode || digitalPersonMode || isTaskText === '任务正在处理中...'
                      }"
                      @click="isTaskText !== '任务正在处理中...' ? selectModel(model.id) : null"
                      @click.stop="isTaskText !== '任务正在处理中...' ? toggleCustomCheck(model.id) : null"
                    >
                      <div
                        class="absolute top-[2px] right-[2px] w-[1vw] h-[1vw] bg-white bg-opacity-60 flex items-center justify-center"
                        :class="{
                          'border-2 border-white': !checkedCustomModels.includes(model.id),
                          'opacity-30': teamCollaboration || storyboardMode || digitalPersonMode || isTaskText === '任务正在处理中...',
                          'cursor-pointer': isTaskText !== '任务正在处理中...',
                          'cursor-not-allowed': isTaskText === '任务正在处理中...'
                        }"
                      >
                        <img
                          v-if="checkedCustomModels.includes(model.id)"
                          src="@/assets/image/ai-team/gou.png"
                          class="w-full h-full object-contain"
                          loading="lazy"
                        />
                      </div>
                      <img
                        :src="model.avatar"
                        :alt="model.name"
                        class="w-full h-full object-cover"
                        loading="lazy"
                      />
                      <div
                      class="absolute top-[2px] left-[2px] w-[1vw] h-[1vw] bg-red-600 bg-opacity-80 flex items-center justify-center transition-all duration-200"
                      :class="{
                        'cursor-pointer hover:bg-opacity-100': isTaskText !== '任务正在处理中...',
                        'cursor-not-allowed opacity-30': isTaskText === '任务正在处理中...'
                      }"
                      @click.stop="isTaskText !== '任务正在处理中...' ? deleteMember(model.id) : null"
                    >
                      <span class="text-white text-[0.6vw] font-bold">×</span>
                    </div>
                      <div
                        class="absolute bottom-0 left-0 right-0 h-[30%] bg-[#000000] bg-opacity-60 flex items-center justify-center text-white text-[1.5vh]"
                      >
                        {{ model.name }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right part (30%) -->
            <div class="h-full bg-[#1B2130] border-l border-[#393B4A]">
              <div
                class="h-full pb-[0.7vh] pt-[2.39vw] px-[0.5vw] flex items-center justify-center w-[100%] box-border"
              >
                <div
                  class="aspect-square h-[100%] bg-[#303A54] border border-[#8B94A3] flex w-[100%] box-border flex-col items-center justify-center transition-colors duration-200 relative"
                  :class="{
                    'cursor-pointer hover:bg-[#3A4451]': isTaskText !== '任务正在处理中...',
                    'cursor-not-allowed opacity-50': isTaskText === '任务正在处理中...'
                  }"
                  @click="isTaskText !== '任务正在处理中...' ? openCreateMemberDialog() : null"
                >
                  <img
                    src="@/assets/image/ai-team/add_task.png"
                    alt="新建"
                    class="w-[1vw] h-[1vw] mb-[0.1vw]"
                    loading="lazy"
                  />
                  <span class="text-[#C4C4C4] text-[0.8vw] font-medium"
                    >新建</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div
      class="flex-grow bg-[#1B2130] border border-[#393B4A] relative flex flex-col"
    >
      <div class="bg-[#1B2130] p-4 relative flex-grow">
        <textarea
          v-model="userInput"
          class="w-full h-full bg-transparent text-white placeholder-gray-400 focus:outline-none resize-none text-[0.9375vw] pr-[8vw] pb-[3vw]"
          placeholder="在此输入提示词..."
          @keydown="handleKeyPress"
          :disabled="isGenerating || isTaskText === '任务正在处理中...'"
        ></textarea>
        <!-- 按钮组和图片预览 - 右下角 -->
        <div
          class="absolute bottom-[1.0416vw] right-[1.0416vw] flex items-end gap-[0.5vw] z-10"
        >
          <!-- 图片预览区域 - 按钮左侧 -->
          <div v-if="uploadedImages.length > 0" class="flex items-end gap-1 max-w-[20vw] overflow-x-hidden -mb-[5px]">
            <div
              v-for="image in uploadedImages"
              :key="image.id"
              class="relative group flex-shrink-0"
            >
              <img
                :src="image.preview"
                :alt="image.name"
                class="w-[2.5vw] h-[2.5vw] object-cover border border-[#393B4A] rounded transition-opacity duration-200"
                :class="{
                  'cursor-pointer hover:opacity-80': isTaskText !== '任务正在处理中...',
                  'cursor-not-allowed opacity-50': isTaskText === '任务正在处理中...'
                }"
                loading="lazy"
                @click="isTaskText !== '任务正在处理中...' ? previewImage(image.id) : null"
              />
              <!-- 删除按钮 -->
              <button
                @click.stop="isTaskText !== '任务正在处理中...' ? removeImage(image.id) : null"
                class="absolute top-0 right-0 w-3 h-3 bg-red-600 text-white rounded-full flex items-center justify-center text-[8px] transition-colors duration-200 opacity-0 group-hover:opacity-100"
                :class="{
                  'hover:bg-red-700 cursor-pointer': isTaskText !== '任务正在处理中...',
                  'cursor-not-allowed opacity-30': isTaskText === '任务正在处理中...'
                }"
                :disabled="isTaskText === '任务正在处理中...'"
              >
                ×
              </button>
            </div>
          </div>

          <!-- 按钮组 -->
          <div class="flex items-center gap-[0.5vw]">
          <!-- 文件按钮 -->
          <el-tooltip
            trigger="hover"
            effect="customadd-top"
            content="上传图片"
            :show-after="100"
            placement="top"
          >
            <button
              class="bg-[#223380] text-white px-[0.6vw] py-[0.1vw] rounded text-[0.8333vw] flex items-center justify-center"
              style="
                box-shadow: 0px 2px 4px 0px rgba(86, 122, 214, 0.6),
                  inset -1px -1px 0px 0px #3354e8;
              "
              @click="handleFileClick"
              :disabled="isGenerating || isTaskText === '任务正在处理中...'"
              :class="{ 'opacity-50 cursor-not-allowed': isGenerating || isTaskText === '任务正在处理中...' }"
            >
              <img
                src="@/assets/image/ai-team/file.png"
                class="w-[1.7vw] h-[1.7vw]"
                loading="lazy"
              />
            </button>
          </el-tooltip>

          <el-tooltip
            trigger="hover"
            effect="customadd-top"
            content="录音"
            :show-after="100"
            placement="top"
          >
            <!-- 录音按钮 -->
            <button
              class="bg-[#223380] text-white px-[0.6vw] py-[0.1vw] rounded text-[0.8333vw] flex items-center justify-center"
              style="
                box-shadow: 0px 2px 4px 0px rgba(86, 122, 214, 0.6),
                  inset -1px -1px 0px 0px #3354e8;
              "
              @click="handleRecordClick"
              :disabled="isGenerating || isTaskText === '任务正在处理中...'"
              :class="{ 'opacity-50 cursor-not-allowed': isGenerating || isTaskText === '任务正在处理中...' }"
            >
              <img
                src="@/assets/image/ai-team/record.png"
                class="w-[1.7vw] h-[1.7vw]"
                loading="lazy"
              />
            </button>
          </el-tooltip>

          <!-- 生成按钮 -->
          <button
            class="bg-[#223380] text-white rounded text-[1vw] py-[0.07vw] px-[0.6vw] flex items-center justify-center gap-[0.1vw] w-auto"
            style="
              box-shadow: 0px 2px 4px 0px rgba(86, 122, 214, 0.6),
                inset -1px -1px 0px 0px #3354e8;
            "
            @click="handleSendMessage"
            :disabled="isGenerating || !userInput.trim() || isTaskText === '任务正在处理中...'"
            :class="{
              'opacity-50 cursor-not-allowed':
                isGenerating || !userInput.trim() || isTaskText === '任务正在处理中...',
            }"
          >
            <img
              src="@/assets/image/ai-team/send.png"
              alt="发送"
              class="w-[1.7vw] h-[1.7vw] flex-shrink-0 mt-[0.1vw]"
              loading="lazy"
            />
            <span class="flex-shrink-0">{{
              isGenerating ? "生成中..." : `发送(${currentModelPoints}积分)`
            }}</span>
          </button>
          </div>
        </div>

        <!-- 隐藏的图片文件输入框 -->
        <input
          ref="imageInputRef"
          type="file"
          accept=".jpg,.jpeg,.png"
          multiple
          class="hidden"
          @change="handleImageUpload"
        />

        <!-- AI图像 - 左下角 -->
        <img
          src="@/assets/image/ai-team/ai.png"
          alt="AI"
          loading="lazy"
          class="ai-avatar absolute bottom-[0.1vw] left-[0.1vw] w-[3vw] h-[3vw] object-contain cursor-pointer transition-all duration-300 ease-in-out hover:scale-110 hover:rotate-12 hover:brightness-125 z-10"
        />
      </div>
    </div>
  </div>

  <!-- 新建员工模态框 -->
  <el-dialog
    v-model="createMemberDialogVisible"
    width="400px"
    :append-to-body="true"
    destroy-on-close
    header-class="custom-header-dialog"
    body-class="custom-body-dialog"
    footer-class="custom-footer-dialog"
    class="custom-dialog"
    :show-close="false"
  >
    <div class="dialog-content">
      <el-form label-position="top">
        <!-- 第一行：姓名和头像 -->
        <div class="flex items-start gap-3">
          <div class="flex-1">
            <el-form-item label="姓名">
              <el-input
                v-model="createMemberForm.name"
                placeholder="请输入员工姓名"
              />
            </el-form-item>
          </div>
          <div class="flex-shrink-0">
            <el-form-item>
              <div class="flex gap-1">
                <span class="el-form-item__label text-white">头像</span>
                <div
                  class="w-16 h-16 flex items-center justify-center cursor-pointer overflow-hidden border border-gray-600 relative bg-[#303A54]"
                  @click="triggerAvatarUpload"
                >
                  <img
                    v-if="createMemberForm.avatar"
                    :src="createMemberForm.avatar"
                    alt="员工头像"
                    class="w-full h-full object-cover"
                    loading="lazy"
                  />
                  <span v-else class="text-white text-[2vw]">+</span>
                  <div
                    v-if="isUploadingAvatar"
                    class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50"
                  >
                    <div
                      class="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"
                    ></div>
                  </div>
                </div>
              </div>
              <input
                ref="avatarInputRef"
                type="file"
                accept=".jpg,.jpeg,.png"
                class="hidden"
                @change="handleAvatarUpload"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 第二行：提示词 -->
        <el-form-item label="提示词">
          <el-input
            v-model="createMemberForm.prompt"
            placeholder="请输入提示词"
          />
        </el-form-item>

        <!-- 温度控制 -->
        <el-form-item label="温度">
          <div class="w-full">
            <div class="flex items-center w-full mb-2 mt-3">
              <span class="text-sm text-white mr-2">0</span>
              <el-slider
                v-model="createMemberForm.temperature"
                :min="0"
                :max="1"
                style="height: 2px"
                size="small"
                :step="0.01"
                class="flex-1 mx-1"
                tooltip-class="custom-tooltip-dialog"
              />
              <span class="text-sm text-white ml-2">1</span>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer flex gap-2 justify-end">
        <button
          class="bg-[#223380] text-white text-[0.8vw] px-7 flex py-[0.1vw] items-center justify-center w-auto"
          style="
            box-shadow: 0px 1px 2px 0px rgba(86, 122, 214, 0.6),
              inset -1px -1px 0px 0px #3354e8;
          "
          @click="createMember"
        >
          确认
        </button>
        <button
          class="bg-[#303A54] text-white px-7 text-[0.8vw] py-[0.1vw]"
          @click="createMemberDialogVisible = false"
        >
          取消
        </button>
      </div>
    </template>
  </el-dialog>

  <!-- 录音模态框 -->
  <el-dialog
    v-model="recordDialogVisible"
    width="500px"
    :append-to-body="true"
    destroy-on-close
    header-class="custom-header-dialog"
    body-class="custom-body-dialog"
    footer-class="custom-footer-dialog"
    class="custom-dialog"
    :show-close="false"
    title="录音"
  >
    <div class="dialog-content">
      <div class="flex flex-col items-center space-y-6 py-4">
        <!-- 录音时间显示 -->
        <div class="text-2xl font-mono text-white">
          {{ formatTime(recordingTime) }}
        </div>

        <!-- 波形显示区域 -->
        <div class="w-full h-24 bg-[#303A54] rounded-lg p-4 flex items-end justify-center space-x-1">
          <div
            v-for="(height, index) in waveformData"
            :key="index"
            class="bg-blue-400 rounded-t transition-all duration-100 ease-out"
            :style="{
              width: '6px',
              height: `${height}%`,
              backgroundColor: isRecording ? '#60A5FA' : '#6B7280'
            }"
          ></div>
        </div>

        <!-- 录音状态提示 -->
        <div class="text-center">
          <p class="text-white text-sm mb-2">
            {{ isRecording ? '正在录音中...' : '点击开始录音' }}
          </p>
          <div v-if="isRecording" class="flex items-center justify-center space-x-2">
            <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span class="text-red-400 text-xs">REC</span>
          </div>
        </div>

        <!-- 录音控制按钮 -->
        <div class="flex space-x-4">
          <button
            v-if="!isRecording"
            @click="startRecording"
            class="bg-[#223380] text-white px-6 py-2 rounded-lg flex items-center space-x-2 hover:bg-[#2a4099] transition-colors"
            style="
              box-shadow: 0px 2px 4px 0px rgba(86, 122, 214, 0.6),
                inset -1px -1px 0px 0px #3354e8;
            "
          >
            <img
              src="@/assets/image/ai-team/record.png"
              class="w-5 h-5"
              loading="lazy"
            />
            <span>开始录音</span>
          </button>

          <button
            v-if="isRecording"
            @click="stopRecording"
            class="bg-red-600 text-white px-6 py-2 rounded-lg flex items-center space-x-2 hover:bg-red-700 transition-colors"
          >
            <div class="w-3 h-3 bg-white rounded-sm"></div>
            <span>停止录音</span>
          </button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer flex gap-2 justify-end">
        <button
          class="bg-[#223380] text-white text-[0.8vw] px-7 flex py-[0.1vw] items-center justify-center w-auto"
          style="
            box-shadow: 0px 1px 2px 0px rgba(86, 122, 214, 0.6),
              inset -1px -1px 0px 0px #3354e8;
          "
          @click="confirmRecording"
          :disabled="recordingTime === 0 || isTranscribing"
          :class="{ 'opacity-50 cursor-not-allowed': recordingTime === 0 || isTranscribing }"
        >
          {{ isTranscribing ? '转录中...' : '确定' }}
        </button>
        <button
          class="bg-[#303A54] text-white px-7 text-[0.8vw] py-[0.1vw]"
          @click="cancelRecording"
        >
          取消
        </button>
      </div>
    </template>
  </el-dialog>

  <!-- AI优化模态框 -->
  <el-dialog
    v-model="aiOptimizeDialogVisible"
    width="600px"
    :append-to-body="true"
    destroy-on-close
    header-class="custom-header-dialog"
    body-class="custom-body-dialog"
    footer-class="custom-footer-dialog"
    class="custom-dialog"
    :show-close="false"
    title="AI优化"
  >
    <div class="dialog-content">
      <div class="flex flex-col h-[300px]">
        <!-- 输入区域 -->
        <div class="flex-grow bg-[#1B2130] border border-[#393B4A] relative flex flex-col">
          <div class="bg-[#1B2130] p-4 relative flex-grow">
            <textarea
              v-model="aiOptimizeInput"
              class="w-full h-full bg-transparent text-white placeholder-gray-400 focus:outline-none resize-none text-[0.9375vw] pr-[8vw] pb-[3vw]"
              placeholder="请输入优化建议或要求..."
              @keydown="handleAIOptimizeKeyPress"
              :disabled="isGenerating"
            ></textarea>

            <!-- 发送按钮 - 右下角 -->
            <div class="absolute bottom-[1.0416vw] right-[1.0416vw] flex items-end gap-[0.5vw] z-10">
              <button
                class="bg-[#223380] text-white rounded text-[1vw] py-[0.07vw] px-[0.6vw] flex items-center justify-center gap-[0.1vw] w-auto"
                style="
                  box-shadow: 0px 2px 4px 0px rgba(86, 122, 214, 0.6),
                    inset -1px -1px 0px 0px #3354e8;
                "
                @click="handleAIOptimizeSend"
                :disabled="isGenerating || !aiOptimizeInput.trim()"
                :class="{
                  'opacity-50 cursor-not-allowed':
                    isGenerating || !aiOptimizeInput.trim(),
                }"
              >
                <span class="flex-shrink-0">{{
                  isGenerating ? "发送中..." : "发送"
                }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>

  <!-- 图片预览器 -->
  <el-image-viewer
    v-if="showImageViewer"
    :url-list="previewImageList"
    :initial-index="currentImageIndex"
    @close="closeImageViewer"
    hide-on-click-modal
    teleported
  />
</template>

<style scoped>
:deep(.el-switch--small .el-switch__label span) {
  font-size: 12px !important;
}
:deep(.el-switch--small.is-checked .el-switch__core .el-switch__action) {
  left: calc(100% - 10px) !important;
}
:deep(.el-switch__core) {
  width: 12px !important;
  height: 12px !important;
  min-width: 22px !important;
}
:deep(.el-switch__action) {
  width: 9px !important;
  height: 9px !important;
}

:deep(.el-slider) {
  --el-slider-main-bg-color: #2b428c;
  --el-slider-height: 4px;
}

:deep(.el-slider__runway) {
  height: 2px !important;
  background-color: #393b4a;
}

:deep(.el-slider__bar) {
  height: 4px !important;
  background-color: #ffffff;
}

:deep(.el-slider__button) {
  width: 12px;
  height: 12px;
  background-color: #2b428c;
  border: 2px solid white;
}

.ai-avatar {
  animation: float 3s ease-in-out infinite;
  filter: drop-shadow(0 0 8px rgba(116, 143, 212, 0.3));
}

.ai-avatar:hover {
  filter: drop-shadow(0 0 15px rgba(116, 143, 212, 0.8))
    drop-shadow(0 0 25px rgba(22, 119, 255, 0.4));
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-4px);
  }
}
.scroll-container::-webkit-scrollbar {
  display: none;
}

.scroll-container {
  -ms-overflow-style: none;
  scrollbar-width: none;
  cursor: grab;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: scroll-position;
  scroll-behavior: auto;
}

.scroll-container:active {
  cursor: grabbing;
}

/* 模态框样式 - 参考account组件 */
:deep(.custom-dialog) {
  background-color: #1b2130;
  border: 1px solid #393b4a;
}

:deep(.custom-header-dialog) {
  background-color: #1b2130;
  border-bottom: 1px solid #393b4a;
  color: white;
}

:deep(.custom-body-dialog) {
  background-color: #1b2130;
  color: white;
}

:deep(.custom-footer-dialog) {
  background-color: #1b2130;
  border-top: 1px solid #393b4a;
}

:deep(.dialog-content .el-form-item__label) {
  color: white !important;
}

:deep(.el-form-item__label) {
  color: white !important;
}

:deep(.dialog-content .el-input__wrapper) {
  background-color: transparent;
  border: 1px solid #393b4a;
  color: white;
}

:deep(.dialog-content .el-input__inner) {
  color: white;
}

:deep(.dialog-content .el-textarea__inner) {
  background-color: transparent;
  border: 1px solid #393b4a;
  color: white;
}

/* Tooltip样式 - 参考LeftPanel */
:deep(.custom-add-image) {
  background-color: #1B2130 !important;
  border: 1px solid #474D59 !important;
  padding: 0 !important;
  color: #C4C4C4 !important;
}

:deep(.custom-add-image .el-popper__arrow::before) {
  background-color: #1B2130 !important;
  border-color: #474D59 !important;
}

:deep(.custom-add-image:hover) {
  color: white !important;
}

/* 分镜模式tooltip样式 */
:deep(.custom-storyboard-tooltip) {
  background-color: #1B2130 !important;
  border: 1px solid #474D59 !important;
  padding: 0 !important;
}

:deep(.custom-storyboard-tooltip .el-popper__arrow::before) {
  background-color: #1B2130 !important;
  border-color: #474D59 !important;
}

/* 聊天滚动条样式 */
.chat-scroll::-webkit-scrollbar {
  width: 4px;
}

.chat-scroll::-webkit-scrollbar-track {
  background: #1B2130;
}

.chat-scroll::-webkit-scrollbar-thumb {
  background: #393B4A;
  border-radius: 2px;
}

.chat-scroll::-webkit-scrollbar-thumb:hover {
  background: #474D59;
}

.chat-scroll {
  scrollbar-width: thin;
  scrollbar-color: #393B4A #1B2130;
}

/* Markdown内容样式 */
.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  color: #FFFFFF;
  margin: 0.5em 0 0.3em 0;
  font-weight: bold;
}

.markdown-content :deep(h1) { font-size: 1.2em; }
.markdown-content :deep(h2) { font-size: 1.1em; }
.markdown-content :deep(h3) { font-size: 1.05em; }
.markdown-content :deep(h4) { font-size: 1em; }
.markdown-content :deep(h5) { font-size: 0.95em; }
.markdown-content :deep(h6) { font-size: 0.9em; }

.markdown-content :deep(p) {
  margin: 0.3em 0;
  line-height: 1.5;
}

.markdown-content :deep(code) {
  background-color: #2A2F3A;
  color: #E6E6E6;
  padding: 0.1em 0.3em;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}

.markdown-content :deep(pre) {
  background-color: #2A2F3A;
  color: #E6E6E6;
  padding: 0.8em;
  border-radius: 6px;
  overflow-x: auto;
  margin: 0.5em 0;
  border: 1px solid #393B4A;
}

.markdown-content :deep(pre code) {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

.markdown-content :deep(blockquote) {
  border-left: 3px solid #748FD4;
  padding-left: 0.8em;
  margin: 0.5em 0;
  color: #C4C4C4;
  font-style: italic;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  margin: 0.3em 0;
  padding-left: 1.2em;
}

.markdown-content :deep(li) {
  margin: 0.2em 0;
  line-height: 1.4;
}

.markdown-content :deep(a) {
  color: #748FD4;
  text-decoration: none;
}

.markdown-content :deep(a:hover) {
  color: #8BA3E8;
  text-decoration: underline;
}

.markdown-content :deep(strong) {
  font-weight: bold;
  color: #FFFFFF;
}

.markdown-content :deep(em) {
  font-style: italic;
  color: #E6E6E6;
}

.markdown-content :deep(table) {
  border-collapse: collapse;
  margin: 0.5em 0;
  width: 100%;
}

.markdown-content :deep(th),
.markdown-content :deep(td) {
  border: 1px solid #393B4A;
  padding: 0.3em 0.6em;
  text-align: left;
}

.markdown-content :deep(th) {
  background-color: #2A2F3A;
  font-weight: bold;
  color: #FFFFFF;
}

.markdown-content :deep(td) {
  background-color: #1F2329;
}

.markdown-content :deep(hr) {
  border: none;
  border-top: 1px solid #393B4A;
  margin: 0.8em 0;
}

.markdown-content :deep(th:first-child),
.markdown-content :deep(td:first-child) {
  width: 18%;
  min-width: 70px;
}

/* 思考中动画 */
.thinking-dots {
  animation: thinking 1.5s infinite;
}

@keyframes thinking {
  0%, 20% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

/* 加载中动画 */
.loading-dots .dot1,
.loading-dots .dot2,
.loading-dots .dot3 {
  animation: loading 1.4s infinite;
}

.loading-dots .dot1 {
  animation-delay: 0s;
}

.loading-dots .dot2 {
  animation-delay: 0.2s;
}

.loading-dots .dot3 {
  animation-delay: 0.4s;
}

@keyframes loading {
  0%, 60%, 100% {
    opacity: 0.3;
  }
  30% {
    opacity: 1;
  }
}

/* 隐藏聊天容器滚动条 */
.chat-scroll-hidden {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.chat-scroll-hidden::-webkit-scrollbar {
  display: none; /* WebKit */
}
</style>
