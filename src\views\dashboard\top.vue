<script setup>
import { ref, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useUserStore } from '@/store/user';

const router = useRouter();
const searchInput = ref("");
const userStore = useUserStore();

const isHomePage = computed(() => {
  return !userStore.getShowBack;
});

const userInfo = computed(() => userStore.getUserData);
const userNickname = computed(() => userInfo.value?.nickname || '加载中...');
const vipExpiry = computed(() => {
  const vipTime = userInfo.value?.vip_time;
  return vipTime ? vipTime.split('T')[0] : '暂未开通';
});
const userPoints = computed(() => userInfo.value?.points ?? '加载中...');

const goBack = () => {
  useUserStore().setShowBack(false);
  router.back();
};
</script>

<template>
  <el-row
    class="h-[4vw] py-[1vw] px-[0.8333vw] items-center border-gray-200"
    justify="space-between"
    align="middle"
  >
    <el-col :span="isHomePage ? 8 : 12">
      <div class="flex items-center">
        <div v-if="!isHomePage"
             class="back-button mr-[0.825vw] cursor-pointer flex items-center bg-gradient-to-l from-[#1C3175] to-[#101529] px-[0.8333vw] py-[1vh]"
             @click="goBack">
          <img src="@/assets/image/dashboard/back_icon.png" alt="返回" class="w-[0.4vw] h-[0.7vw] mr-[0.6vw]" loading="lazy">
          <span class="text-white text-[0.8292vw]">返回</span>
        </div>
        <div class="flex flex-grow items-center bg-[#1B2130] px-[1vw] py-[0.5vw] border border-solid border-[#8B94A3] rounded-[12px]">
          <input
            class="flex-grow bg-transparent outline-none text-[0.8292vw] text-white placeholder-[#8B94A3]"
            type="text"
            placeholder="请输入关键字"
            v-model="searchInput"
          />
          <div class="flex cursor-pointer justify-center items-center">
            <img src="@/assets/image/dashboard/search.png" alt="搜索" class="w-[1vw] h-[1.1vw] ml-[0.4167vw]" loading="lazy">
            <span class="text-white text-[0.8292vw] ml-[0.2083vw]">搜索</span>
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="isHomePage ? 16 : 12">
      <div class="flex items-center justify-end space-x-[1.25vw]">
        <div
          class="flex items-center font-medium space-x-[0.8333vw] text-[0.8292vw] bg-[#1B2130] px-[1vw] py-[0.5vw] border border-solid border-[#8B94A3]"
        >
          <span class="text-white">用户名: {{ userNickname }}</span>
          <span class="text-white"
            >到期时间: <span class="text-[#E09010]">{{ vipExpiry }}</span></span
          >
          <span class="text-white"
            >剩余点数: <span class="text-[#E09010]">{{ userPoints }}</span></span
          >
        </div>
        <div class="back-button mr-[0.625vw] cursor-pointer flex items-center bg-gradient-to-l from-[#1C3175] to-[#101529] px-[0.8333vw] py-[0.3704vh]">
          <span class="text-white text-[0.9vw] py-[0.3vw]">点击充值</span>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<style lang="scss" scoped>
.back-button {
  box-shadow: 0px 10px 30px -10px rgba(67,97,238,0.55);
}
</style>
