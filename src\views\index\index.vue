<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { post } from '@/request';
import { ElMessage } from 'element-plus';
import Cookies from 'js-cookie';
import { useUserStore } from '@/store/user';

const router = useRouter();
const activeTab = ref('login');
const userStore = useUserStore();
const title = computed(() => {
  return activeTab.value === 'login' ? '欢迎登录' : '欢迎注册';
})

const loginForm = reactive({
  username: '',
  password: '',
});

const registerForm = reactive({
  mobile: '',
  password: '',
  confirm_password: '',
  captcha: '',
});

const isSendingCaptcha = ref(false);
const countdown = ref(0);

const sendCaptcha = async () => {
  if (isSendingCaptcha.value || !registerForm.mobile) {
    ElMessage.warning('请输入手机号');
    return;
  }
  const res = await post('/api/send-captcha', { mobile: registerForm.mobile, type: 'register'});
  if (res.code === 200) {
    ElMessage.success(res.message);
    countdown.value = 60;
    const timer = setInterval(() => {
      countdown.value--;
      if (countdown.value <= 0) {
        clearInterval(timer);
        isSendingCaptcha.value = false;
      }
    }, 1000);
  }
};

const handleRegister = async () => {
  if (registerForm.password !== registerForm.confirm_password) {
    ElMessage.error('两次输入的密码不一致');
    return;
  }
  const res = await post('/api/register', registerForm);
    if (res.data.access_token) {
      ElMessage.success('注册成功！');
      Cookies.set('access_token', res.data.access_token);
      router.push('/dashboard');
    }
};

const handleLogin = async () => {
  const res = await post('/api/login', loginForm, { showError: true, level: 0 });
  if (res.data.access_token) {
    ElMessage.success('登录成功！');
    Cookies.set('access_token', res.data.access_token);
    router.push('/dashboard');
  }
};

onMounted(() => {
  const access_token = Cookies.get('access_token');
  if (access_token) {
    router.push('/dashboard');
  } else {
    userStore.clearUserData();
  }
})
</script>

<template>
  <div class="flex items-center justify-center h-screen bg-cover bg-center w-screen background-image">
    <div class="flex w-[70.6363vw] h-[69vh] bg-[#1B2130] rounded-lg shadow-xl overflow-hidden">
      <div class="w-1/2">
        <img src="@/assets/image/index/ai_background.png" class="object-cover w-full h-full" alt="AI Background" loading="lazy">
      </div>

      <div class="w-1/2 p-[2vw] text-white flex flex-col justify-center">
        <h2 class="text-[1.6vw] font-bold text-center mb-[1.5vw]">{{  title }}</h2>

        <div class="flex mb-[1.3vw] border-b border-[#5380ED]">
          <button
            @click="activeTab = 'login'"
            :class="['py-[0.2vw] px-[1vw] mr-[0.5vw] focus:outline-none text-[1vw]', activeTab === 'login' ? 'border-b-2 border-white text-white' : 'text-gray-400 hover:text-gray-200']"
          >
            账号登录
          </button>
          <button
            @click="activeTab = 'register'"
            :class="['py-[0.2vw] px-[1vw] focus:outline-none text-[1vw]', activeTab === 'register' ? 'border-b-2 border-white text-white' : 'text-gray-400 hover:text-gray-200']"
          >
            账号注册
          </button>
        </div>

        <!-- 登录表单 -->
        <form v-if="activeTab === 'login'" @submit.prevent="handleLogin">
          <div class="mb-[1.2vw] relative border border-[#5380ED] rounded overflow-hidden">
            <span class="absolute inset-y-0 left-0 flex items-center pl-[0.8vw]">
              <img src="@/assets/image/index/user.png" class="h-[1.3vw] w-[1.3vw]" alt="User Icon" loading="lazy">
            </span>
            <input
              v-model="loginForm.username"
              type="text"
              required
              placeholder="请输入账号"
              class="w-full pl-[2.5vw] pr-[0.8vw] py-[0.6vw] bg-transparent text-[0.9vw] border-none focus:outline-none focus:ring-0 text-white placeholder-gray-500"
            />
          </div>
          <div class="mb-[1vw] relative border border-[#5380ED] rounded overflow-hidden">
            <span class="absolute inset-y-0 left-0 flex items-center pl-[0.8vw]">
               <img src="@/assets/image/index/password.png" class="h-[1.3vw] w-[1.3vw]" alt="Password Icon" loading="lazy">
            </span>
            <input
              v-model="loginForm.password"
              type="password"
              required
              placeholder="请输入密码"
              class="w-full pl-[2.5vw] pr-[0.8vw] py-[0.6vw] text-[0.9vw] bg-transparent border-none focus:outline-none focus:ring-0 text-white placeholder-gray-500"
            />
          </div>
          <div class="mb-[2vh] text-[0.8vw] text-gray-400">
            登录即代表同意 <a href="/policy" target="_blank" class="text-blue-400 hover:underline">《用户协议》</a> 和 <a href="/policy" target="_blank" class="text-blue-400 hover:underline">《隐私协议》</a>
          </div>
          <button
            type="submit"
            class="w-full bg-gradient-to-t from-[#243670] to-[#0C153D] hover:bg-gradient-to-b hover:from-[#243670] hover:to-[#0C153D] text-white font-bold py-[1vh] px-[1vw] rounded focus:outline-none focus:shadow-outline mb-[0.6vw] transition-all text-[0.9vw]"
          >
            立即登录
          </button>
          <div class="text-right">
            <a href="#" class="text-[0.9vw] text-white cursor-pointer hover:underline">忘记密码?</a>
          </div>
        </form>

        <!-- 注册表单 -->
        <form v-if="activeTab === 'register'" @submit.prevent="handleRegister">
          <div class="mb-[1.2vw] relative border border-[#5380ED] rounded overflow-hidden">
            <span class="absolute inset-y-0 left-0 flex items-center pl-[0.8vw]">
              <img src="@/assets/image/index/user.png" class="h-[1vw] w-[1vw]" alt="User Icon" loading="lazy">
            </span>
            <input
              v-model="registerForm.mobile"
              type="text"
              required
              placeholder="请输入手机号"
              class="w-full pl-[2.5vw] pr-[0.8vw] py-[0.6vw] text-[0.9vw] bg-transparent border-none focus:outline-none focus:ring-0 text-white placeholder-gray-500"
            />
          </div>
          <div class="mb-[1.2vw] relative border border-[#5380ED] rounded overflow-hidden">
            <span class="absolute inset-y-0 left-0 flex items-center pl-[0.8vw]">
              <img src="@/assets/image/index/password.png" class="h-[1vw] w-[1vw]" alt="Password Icon" loading="lazy">
            </span>
            <input
              v-model="registerForm.password"
              type="password"
              required
              placeholder="请输入密码 (6-20位字母、数字或下划线)"
              pattern="^[a-zA-Z0-9_]{6,20}$"
              title="密码必须为6-20位字母、数字或下划线"
              class="w-full pl-[2.5vw] pr-[0.8vw] py-[0.6vw] text-[0.9vw] bg-transparent border-none focus:outline-none focus:ring-0 text-white placeholder-gray-500"
            />
          </div>
          <div class="mb-[1.2vw] relative border border-[#5380ED] rounded overflow-hidden">
            <span class="absolute inset-y-0 left-0 flex items-center pl-[0.8vw]">
              <img src="@/assets/image/index/password.png" class="h-[1vw] w-[1vw]" alt="Password Icon" loading="lazy">
            </span>
            <input
              v-model="registerForm.confirm_password"
              type="password"
              required
              placeholder="请二次确认密码"
              class="w-full pl-[2.5vw] pr-[0.8vw] py-[0.6vw] text-[0.9vw] bg-transparent border-none focus:outline-none focus:ring-0 text-white placeholder-gray-500"
            />
          </div>
          <div class="mb-[1.2vw] relative border border-[#5380ED] rounded overflow-hidden">
            <span class="absolute inset-y-0 left-0 flex items-center pl-[0.8vw]">
               <img src="@/assets/image/index/captcha.png" class="h-[1vw] w-[1vw]" alt="Captcha Icon" loading="lazy">
            </span>
            <input
              v-model="registerForm.captcha"
              type="text"
              required
              placeholder="请输入验证码"
              class="w-full pl-[2.5vw] pr-[7vw] py-[0.6vw] text-[0.9vw] bg-transparent border-none focus:outline-none focus:ring-0 text-white placeholder-gray-500"
            />
            <button
              type="button"
              @click="sendCaptcha"
              :disabled="isSendingCaptcha"
              class="absolute inset-y-[4px] right-0 flex items-center px-[0.8vw] text-[0.8vw] text-white focus:outline-none bg-[#244275] rounded-md mr-[0.2vw] hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {{ isSendingCaptcha ? `${countdown}s后重发` : '获取验证码' }}
            </button>
          </div>
          <div class="mb-[1vw] text-[0.8vw] text-gray-400">
            注册即代表同意 <a href="/policy" target="_blank" class="text-blue-400 hover:underline">《用户协议》</a> 和 <a href="/policy" target="_blank" class="text-blue-400 hover:underline">《隐私协议》</a>
          </div>
          <button
            type="submit"
            class="w-full bg-gradient-to-t from-[#243670] to-[#0C153D] hover:bg-gradient-to-b hover:from-[#243670] hover:to-[#0C153D] text-white font-bold py-[1vh] px-[1vw] rounded focus:outline-none focus:shadow-outline mb-[0.6vw] transition-all text-[0.9vw]"
          >
            立即创作
          </button>
        </form>

      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.background-image {
  background-image: url('@/assets/image/index/background.png');
  background-size: cover;
}
</style>