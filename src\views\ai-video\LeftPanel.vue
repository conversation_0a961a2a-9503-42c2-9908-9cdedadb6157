<script setup>
import { ref } from 'vue'
import { ElTooltip } from 'element-plus'
import addImagePlaceholder from '@/assets/image/ai-video/add_image.png'

// Props
const props = defineProps({
  tabs: Array,
  activeTab: String,
  apiEndpoints: Object
})

// Emits
const emit = defineEmits(['update:active-tab', 'refresh-history'])

// 状态
const selectedRatio = ref('1:1')
const selectedLens = ref('智能匹配')
const referenceImages = ref([null, null, null])
const fileInputs = ref([])
const firstFrameImage = ref(null)
const lastFrameImage = ref(null)
const firstFrameInput = ref(null)
const lastFrameInput = ref(null)

// 数据
const imageRatios = ref([
  { ratio: '1:1', text: '头像框' },
  { ratio: '16:9', text: '电脑壁纸' },
  { ratio: '9:16', text: '文章配图' }
])

const lenses = ref([
  { name: '智能匹配', prompt: '保持平稳，背景与人物动态连贯' },
  { name: '水平运镜', prompt: '水平移动镜头，保持平稳，从左至右展现全景，背景与人物动态连贯。' },
  { name: '垂直运镜', prompt: '垂直上下移动镜头' },
  { name: '拉近', prompt: '镜头从远处推进' },
  { name: '拉远', prompt: '镜头从近处拉远' },
  { name: '水平摇镜', prompt: '水平摇镜' },
  { name: '垂直摇镜', prompt: '垂直摇镜' },
  { name: '旋转摇镜', prompt: '镜头以中心轴旋转360度' }
])

// 方法
const triggerFileInput = (index) => {
  fileInputs.value[index].click()
}

const handleFileChange = (event, index) => {
  const file = event.target.files[0]
  if (file) {
    const validTypes = ['image/png', 'image/jpeg', 'image/jpg']
    if (validTypes.includes(file.type)) {
      const reader = new FileReader()
      reader.onload = (e) => {
        referenceImages.value[index] = e.target.result
      }
      reader.readAsDataURL(file)
    } else {
      console.error('Invalid file type. Please select a PNG, JPG, or JPEG image.')
      event.target.value = null
    }
  }
}

const removeImage = (index) => {
  referenceImages.value[index] = null
  if (fileInputs.value[index]) {
    fileInputs.value[index].value = null
  }
}

const triggerFirstFrameUpload = () => {
  firstFrameInput.value.click()
}

const triggerLastFrameUpload = () => {
  lastFrameInput.value.click()
}

const handleFrameImageUpload = (event, isFirstFrame) => {
  const file = event.target.files[0]
  if (!file) return

  const validTypes = ['image/png', 'image/jpeg', 'image/jpg']
  if (!validTypes.includes(file.type)) {
    alert('请上传 JPG、JPEG 或 PNG 格式的图片')
    event.target.value = ''
    return
  }

  if (file.size > 5 * 1024 * 1024) {
    alert('图片大小不能超过5MB')
    event.target.value = ''
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    if (isFirstFrame) {
      firstFrameImage.value = e.target.result
    } else {
      lastFrameImage.value = e.target.result
    }
  }
  reader.readAsDataURL(file)
}

const removeFrameImage = (isFirstFrame) => {
  if (isFirstFrame) {
    firstFrameImage.value = null
    if (firstFrameInput.value) {
      firstFrameInput.value.value = null
    }
  } else {
    lastFrameImage.value = null
    if (lastFrameInput.value) {
      lastFrameInput.value.value = null
    }
  }
}

const swapFrameImages = () => {
  const tempImage = firstFrameImage.value
  firstFrameImage.value = lastFrameImage.value
  lastFrameImage.value = tempImage
}

// 暴露给父组件的数据
defineExpose({
  selectedRatio,
  selectedLens,
  referenceImages,
  firstFrameImage,
  lastFrameImage,
  lenses
})
</script>

<template>
  <div class="w-[27.28vw] bg-[#1B2130] border border-[#393B4A] p-[0.8333vw] space-y-3 overflow-y-auto text-white overflow-hidden scrollbar-hide">
    <div class="flex border-b border-[#393B4A] mb-3">
      <div
        v-for="tab in tabs"
        :key="tab.id"
        class="py-2 px-4 cursor-pointer text-center text-[1vw] font-['AppleSystemUIFont'] flex-1"
        :class="activeTab === tab.name ? 'text-white border-b-2 border-white' : 'text-[#AAAAAA]'"
        @click="emit('update:active-tab', tab.name)"
      >
        {{ tab.name }}
      </div>
    </div>

    <!-- 文生视频 -->
    <div v-if="activeTab === '文生视频'">
      <div>
        <h3 class="text-[0.9vw] mb-2 ml-2 font-['AppleSystemUIFont']">图片比例选择</h3>
        <div class="grid grid-cols-3 gap-[6px]">
          <div
            v-for="item in imageRatios"
            :key="item.ratio"
            class="p-[0.6166vw] cursor-pointer text-center border font-['AppleSystemUIFont'] flex flex-col items-center h-[5.2vw] pb-[0.2083vw]"
            :class="selectedRatio === item.ratio ? 'bg-[#303A54] border-[#8B94A3]' : 'bg-[#273047] border-[#393B4A]'"
            @click="selectedRatio = item.ratio"
          >
            <div class="h-[1.6666vw] flex items-center justify-center mb-[0.2083vw]">
              <div
                class="rounded-md border"
                :class="[
                  {
                    'w-[1.6vw] h-[1.6vw]': item.ratio === '1:1',
                    'w-[2.9vw] h-[1.6vw]': item.ratio === '16:9',
                    'w-[1.4vw] h-[2.3vw]': item.ratio === '9:16'
                  },
                  selectedRatio === item.ratio ? 'bg-[#424E6E]' : ''
                ]"
              ></div>
            </div>
            <div class="flex-grow"></div>
            <div class="text-[0.8vw] leading-tight" :class="selectedRatio === item.ratio ? 'text-white' : 'text-[#C4C4C4]'">{{ item.ratio }}</div>
            <div class="text-[0.8vw] leading-tight" :class="selectedRatio === item.ratio ? 'text-white' : 'text-[#C4C4C4]'">{{ item.text }}</div>
          </div>
        </div>
      </div>

      <div class="mt-2">
        <h3 class="text-[0.9vw] mb-2 ml-2 font-['AppleSystemUIFont']">镜头</h3>
        <div class="grid grid-cols-4 gap-[6px]">
          <div
            v-for="lens in lenses"
            :key="lens.name"
            class="p-[0.2083vw] cursor-pointer text-center text-[0.9vw] border font-['AppleSystemUIFont']"
            :class="selectedLens === lens.name ? 'bg-[#303A54] border-[#8B94A3]' : 'bg-[#273047] border-[#393B4A]'"
            @click="selectedLens = lens.name"
          >
            {{ lens.name }}
          </div>
        </div>
      </div>
    </div>

    <!-- 图生视频 -->
    <div v-if="activeTab === '图生视频'">
      <div>
        <h3 class="text-[0.9vw] mb-2 ml-2 font-['AppleSystemUIFont']">上传首尾帧图</h3>
        <div class="relative flex items-stretch gap-3">
          <!-- 首帧图片上传 -->
          <el-tooltip
            placement="right"
            :offset="-30"
            popper-class="custom-add-image"
            trigger="hover"
            effect="customadd"
            :disabled="!!firstFrameImage"
          >
            <template #default>
              <div
                class="flex-1 h-[6.1vw] bg-[#303A54] border border-[#8B94A3] flex flex-row items-center justify-center cursor-pointer relative"
                @click="triggerFirstFrameUpload"
              >
                <template v-if="firstFrameImage">
                  <img :src="firstFrameImage" alt="首帧图片" class="w-full h-full object-cover" loading="lazy">
                  <button
                    @click.stop="removeFrameImage(true)"
                    class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs z-10"
                  >
                    ×
                  </button>
                </template>
                <template v-else>
                  <img src="@/assets/image/ai-video/upload.png" alt="upload icon" class="w-[1.7vw] h-[1.7vw] mb-1" loading="lazy">
                  <span class="text-[0.7vw] text-[#C4C4C4] ml-1">上传首帧图片</span>
                </template>
                <input
                  ref="firstFrameInput"
                  type="file"
                  accept=".jpg,.jpeg,.png"
                  class="hidden"
                  @change="event => handleFrameImageUpload(event, true)"
                />
              </div>
            </template>
            <template #content>
              <div @click="triggerFirstFrameUpload" class="text-[0.75vw] leading-tight text-[#C4C4C4] py-[0.1vw] px-[0.5vw] cursor-pointer hover:text-white">从本地上传</div>
              <div class="text-[0.75vw] leading-tight text-[#C4C4C4] py-[0.1vw] px-[0.5vw] cursor-pointer mt-1 hover:text-white">作品库上传</div>
            </template>
          </el-tooltip>

          <!-- 尾帧图片上传 -->
          <el-tooltip
            placement="right"
            :offset="-30"
            popper-class="custom-add-image"
            trigger="hover"
            effect="customadd"
            :disabled="!!lastFrameImage"
          >
            <template #default>
              <div
                class="flex-1 h-[6.1vw] bg-[#303A54] border border-[#8B94A3] flex flex-row items-center justify-center cursor-pointer relative"
                @click="triggerLastFrameUpload"
              >
                <template v-if="lastFrameImage">
                  <img :src="lastFrameImage" alt="尾帧图片" class="w-full h-full object-cover" loading="lazy">
                  <button
                    @click.stop="removeFrameImage(false)"
                    class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs z-10"
                  >
                    ×
                  </button>
                </template>
                <template v-else>
                  <img src="@/assets/image/ai-video/upload.png" alt="upload icon" class="w-[1.7vw] h-[1.7vw] mb-1" loading="lazy">
                  <span class="text-[0.7vw] text-[#C4C4C4] ml-1">上传尾帧图片</span>
                </template>
                <input
                  ref="lastFrameInput"
                  type="file"
                  accept=".jpg,.jpeg,.png"
                  class="hidden"
                  @change="event => handleFrameImageUpload(event, false)"
                />
              </div>
            </template>
            <template #content>
              <div @click="triggerLastFrameUpload" class="text-[0.75vw] leading-tight text-[#C4C4C4] py-[0.1vw] px-[0.5vw] cursor-pointer hover:text-white">从本地上传</div>
              <div class="text-[0.75vw] leading-tight text-[#C4C4C4] py-[0.1vw] px-[0.5vw] cursor-pointer mt-1 hover:text-white">作品库上传</div>
            </template>
          </el-tooltip>

          <!-- 交换按钮 -->
          <img
            src="@/assets/image/ai-video/change.png"
            alt="change"
            loading="lazy"
            class="absolute w-[2.3vw] h-[2.3vw] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10 cursor-pointer"
            @click="swapFrameImages"
          >
        </div>
      </div>

      <div class="mt-2">
        <h3 class="text-[0.9vw] mb-2 ml-2 font-['AppleSystemUIFont']">镜头</h3>
        <div class="grid grid-cols-4 gap-[6px]">
          <div
            v-for="lens in lenses"
            :key="lens.name"
            class="p-[0.2083vw] cursor-pointer text-center text-[0.9vw] border font-['AppleSystemUIFont']"
            :class="selectedLens === lens.name ? 'bg-[#303A54] border-[#8B94A3]' : 'bg-[#273047] border-[#393B4A]'"
            @click="selectedLens = lens.name"
          >
            {{ lens.name }}
          </div>
        </div>
      </div>
    </div>

    <!-- 参考生视频 -->
    <div v-if="activeTab === '参考生视频'">
      <div class="mb-2">
        <h3 class="text-[0.9vw] mb-2 ml-2 font-['AppleSystemUIFont']">参考图添加</h3>
        <div class="flex items-stretch gap-[6px]">
          <el-tooltip
            v-for="(image, index) in referenceImages"
            :key="index"
            placement="right"
            :offset="-30"
            popper-class="custom-add-image"
            trigger="hover"
            effect="customadd"
            :disabled="!!referenceImages[index]"
          >
            <template #default>
              <div
                class="flex-1 h-[6.1vw] bg-[#303A54] border border-[#8B94A3] flex flex-col items-center justify-center cursor-pointer bg-cover bg-center relative"
                :style="{ backgroundImage: referenceImages[index] ? 'none' : `url(${addImagePlaceholder})` }"
                @click="!referenceImages[index] && triggerFileInput(index)"
              >
                <img v-if="referenceImages[index]" :src="referenceImages[index]" loading="lazy" alt="Reference Image" class="w-full h-full object-cover">
                <button
                  v-if="referenceImages[index]"
                  @click.stop="removeImage(index)"
                  class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs z-10">
                  ×
                </button>
                <input
                  type="file"
                  :ref="el => fileInputs[index] = el"
                  @change="event => handleFileChange(event, index)"
                  accept=".png,.jpg,.jpeg"
                  class="hidden"
                />
              </div>
            </template>
            <template #content>
              <div @click="triggerFileInput(index)" class="text-[0.75vw] leading-tight text-[#C4C4C4] py-[0.1vw] px-[0.5vw] cursor-pointer hover:text-white">从本地上传</div>
              <div class="text-[0.75vw] leading-tight text-[#C4C4C4] py-[0.1vw] px-[0.5vw] cursor-pointer mt-1 hover:text-white">作品库上传</div>
            </template>
          </el-tooltip>
        </div>
      </div>

      <div class="mb-2">
        <h3 class="text-[0.9vw] mb-2 ml-2 font-['AppleSystemUIFont']">图片比例选择</h3>
        <div class="grid grid-cols-3 gap-[6px]">
          <div
            v-for="item in imageRatios"
            :key="item.ratio"
            class="p-[0.6166vw] cursor-pointer text-center border font-['AppleSystemUIFont'] flex flex-col items-center h-[5.2vw] pb-[0.2083vw]"
            :class="selectedRatio === item.ratio ? 'bg-[#303A54] border-[#8B94A3]' : 'bg-[#273047] border-[#393B4A]'"
            @click="selectedRatio = item.ratio"
          >
            <div class="h-[1.6666vw] flex items-center justify-center mb-[0.2083vw]">
              <div
                class="rounded-md border"
                :class="[
                  {
                    'w-[1.6vw] h-[1.6vw]': item.ratio === '1:1',
                    'w-[2.9vw] h-[1.6vw]': item.ratio === '16:9',
                    'w-[1.4vw] h-[2.3vw]': item.ratio === '9:16'
                  },
                  selectedRatio === item.ratio ? 'bg-[#424E6E]' : ''
                ]"
              ></div>
            </div>
            <div class="flex-grow"></div>
            <div class="text-[0.8vw] leading-tight" :class="selectedRatio === item.ratio ? 'text-white' : 'text-[#C4C4C4]'">{{ item.ratio }}</div>
            <div class="text-[0.8vw] leading-tight" :class="selectedRatio === item.ratio ? 'text-white' : 'text-[#C4C4C4]'">{{ item.text }}</div>
          </div>
        </div>
      </div>

      <div>
        <h3 class="text-[0.9vw] mb-2 ml-2 font-['AppleSystemUIFont']">镜头</h3>
        <div class="grid grid-cols-4 gap-[6px]">
          <div
            v-for="lens in lenses"
            :key="lens.name"
            class="p-[0.2083vw] cursor-pointer text-center text-[0.9vw] border font-['AppleSystemUIFont']"
            :class="selectedLens === lens.name ? 'bg-[#303A54] border-[#8B94A3]' : 'bg-[#273047] border-[#393B4A]'"
            @click="selectedLens = lens.name"
          >
            {{ lens.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

:deep(.custom-add-image) {
  background-color: #1B2130 !important;
  border: 1px solid #474D59 !important;
  padding: 0 !important;
}

:deep(.custom-add-image .el-popper__arrow::before) {
  background-color: #1B2130 !important;
  border-color: #474D59 !important;
}
</style>
