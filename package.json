{"name": "aigc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"aigc": "file:", "animate.css": "^4.1.1", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "element-plus": "^2.9.9", "js-cookie": "^3.0.5", "markdown-it": "^14.1.0", "pinia": "^3.0.2", "postcss": "^8.5.3", "tailwindcss": "3.4.17", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.2", "sass": "^1.87.0", "sass-loader": "^16.0.5", "unplugin-auto-import": "^19.1.2", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.1"}}